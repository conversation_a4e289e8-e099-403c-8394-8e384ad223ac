import request from '/@/utils/request'

// 识别富文本框内容
export function identityEditor(params: object) {
  return request({
    url: '/announcement/identity',
    method: 'post',
    data: params
  })
}

// 获取公告发布参数
export function getAddParams() {
  return request({
    url: '/announcement/get-add-params'
  })
}

// 获取类型单位
export function getCompanyTypeList(params: Object) {
  return request({
    url: '/announcement/search-company-list',
    params
  })
}

// 获取关联单位
export function getAllCompanyList(params: Object) {
  return request({
    url: '/announcement/get-all-company-list',
    params
  })
}

// 公告列表(合作)
export function getAnnouncementList(params: Object) {
  return request({
    url: '/announcement/cooperation-index',
    params
  })
}

// 公告列表(非合作)
export function getAnnouncementUnCooperationList(params: Object) {
  return request({
    url: '/announcement/un-cooperation-index',
    params
  })
}

// 公告列表
export function getSimpleAnnouncementList(params: Object) {
  return request({
    url: '/announcement/get-simple-list',
    params
  })
}

// 获取公告列表检索参数
export function getAnnounceSearchParams() {
  return request({
    url: '/announcement/get-search-params'
  })
}

/**
 *模糊查找职位列表
 * @returns 返回接口数据
 */
export function getSearchJobList(params: Object) {
  return request({
    url: '/job/template-list',
    method: 'get',
    params
  })
}

// 获取职位编辑数据
export function getJobEidtData(params: Object) {
  return request({
    url: '/announcement/job-edit-data',
    params
  })
}

// 点击编辑数据回显
export function getTemporaryJobEidt(params: Object) {
  return request({
    url: '/announcement/job-edit-init',
    params
  })
}

// 添加公告职位
export function temporaryJobSave(params: object) {
  return request({
    url: '/announcement/temp-job-add',
    method: 'post',
    data: params
  })
}

// 编辑公告职位
export function temporaryJobEdit(params: object) {
  return request({
    url: '/announcement/temp-job-edit',
    method: 'post',
    data: params
  })
}

// 公告职位-删除
export function temporaryJobDelete(params: object) {
  return request({
    url: '/announcement/job-delete',
    method: 'post',
    data: params
  })
}

// 公告职位-复制
export function temporaryJobCopy(params: object) {
  return request({
    url: '/announcement/temp-job-copy',
    method: 'post',
    data: params
  })
}

// 公告添加、保存
export function announcementAdd(params: object) {
  return request({
    url: '/announcement/add',
    method: 'post',
    data: params
  })
}

// 公告添加、保存
export function announcementEdit(params: object) {
  return request({
    url: '/announcement/edit',
    method: 'post',
    data: params
  })
}

// 公告操作-审核详情
export function getAuditDetail(params: object) {
  return request({
    url: '/announcement/audit-detail',
    params
  })
}

/* 公告操作-审核
1审核通过，-1审核拒绝，2审核拒绝并编辑
 */
export function announcementAuditHandle(params: object) {
  return request({
    url: '/announcement/audit-status',
    method: 'post',
    data: params
  })
}

// 公告操作-删除
export function announcementDelete(params: object) {
  return request({
    url: '/announcement/delete',
    method: 'post',
    data: params
  })
}

// 公告操作-下线
export function announcementOffLine(params: object) {
  return request({
    url: '/announcement/offline',
    method: 'post',
    data: params
  })
}

// 公告操作-再发布
export function announcementRepublish(params: object) {
  return request({
    url: '/announcement/republish',
    method: 'post',
    data: params
  })
}

// 公告操作-显示
export function announcementIsShow(params: object) {
  return request({
    url: '/announcement/show',
    method: 'post',
    data: params
  })
}

// 公告操作-隐藏
export function announcementIsHide(params: object) {
  return request({
    url: '/announcement/hide',
    method: 'post',
    data: params
  })
}

// 公告批量操作-隐藏/显示
export function batchHidden(params: object) {
  return request({
    url: '/announcement/batch-hidden-show',
    method: 'post',
    data: { ...params, actionType: '2' }
  })
}
export function batchShow(params: object) {
  return request({
    url: '/announcement/batch-hidden-show',
    method: 'post',
    data: { ...params, actionType: '1' }
  })
}

// 公告操作-编辑
export function getAnnouncementEditInit(params: object) {
  return request({
    url: '/announcement/edit-init',
    params
  })
}

// 职位列表

export function getAnnouncementJobList(params: object) {
  return request({
    url: '/announcement/job-list',
    params
  })
}

// 简单的职位列表
export function getAnnouncementSimpleJobList(params: object) {
  return request({
    url: '/announcement/simple-job-list',
    params
  })
}

// 公告详情
export function getAnnouncementDetail(params: object) {
  return request({
    url: '/announcement/detail',
    params
  })
}

// 公告操作-批量复制

export function announcementBatchCopy(params: object) {
  return request({
    url: '/announcement/batch-copy',
    method: 'post',
    data: params
  })
}

// 公告操作-批量移动

export function announcementBatchMove(params: object) {
  return request({
    url: '/announcement/batch-move',
    method: 'post',
    data: params
  })
}

// 批量操作-删除

export function announcementBatchDel(params: object) {
  return request({
    url: '/announcement/batch-delete',
    method: 'post',
    data: params
  })
}

// 批量操作-审核

export function announcementBatchAudit(params: object) {
  return request({
    url: '/announcement/batch-audit',
    method: 'post',
    data: params
  })
}

// 批量操作-编辑属性

export function announcementBatchEdit(params: object) {
  return request({
    url: '/announcement/change-attribute',
    method: 'post',
    data: params
  })
}

// 批量操作-再发布/下线

export function announcementBatchOnline(params: object) {
  return request({
    url: '/announcement/batch-online-offline',
    method: 'post',
    data: params
  })
}

// 批量操作-刷新

export function announcementBatchRefresh(params: object) {
  return request({
    url: '/announcement/batch-refresh',
    method: 'post',
    data: params
  })
}

// 职位单个操作-再发布/下线

export function announcementJobOnline(params: object) {
  return request({
    url: '/announcement/online-offline-job',
    method: 'post',
    data: params
  })
}

// 公告操作-刷新

export function announcementRefresh(params: object) {
  return request({
    url: '/announcement/refresh',
    method: 'post',
    data: params
  })
}

// 职位操作-刷新

export function announcementRefreshJob(params: object) {
  return request({
    url: '/announcement/refresh-job',
    method: 'post',
    data: params
  })
}

// 职位操作-隐藏/显示

export function jobIsShow(params: object) {
  return request({
    url: '/announcement/hidden-show-job',
    method: 'post',
    data: params
  })
}

// 职位批量操作-刷新

export function jobBatchRefresh(params: object) {
  return request({
    url: '/announcement/batch-refresh-job',
    method: 'post',
    data: params
  })
}

// 职位批量操作-再发布/下线

export function jobBatchOnline(params: object) {
  return request({
    url: '/announcement/batch-online-job',
    method: 'post',
    data: params
  })
}

// 批量新增职位

export function jobBatchAdd(params: object) {
  return request({
    url: '/announcement/job-temporary-batch-import',
    method: 'post',
    data: params
  })
}

// 公告操作-刷新属性排序时间

export function announcementRefreshSortTime(params: object) {
  return request({
    url: '/announcement/refresh-sort-time',
    method: 'post',
    data: params
  })
}

// 公告文档属性数据

export function getAnnouncementAttributeData(params: object) {
  return request({
    url: '/announcement/attribute-data',
    params
  })
}

// 投递列表

export function getAnnouncementOffJobApplyList(params: object) {
  return request({
    url: '/announcement/off-job-apply-list',
    params
  })
}

// 公告审核列表(合作单位)
export function getAnnouncementAuditList(params: object) {
  return request({
    url: '/announcement/cooperation-audit-index',
    params
  })
}

// 公告审核列表(非合作单位)
export function getCmsAnnouncementAuditList(params: object) {
  return request({
    url: '/announcement/un-cooperation-audit-index',
    params
  })
}

// 检查公告标题唯一性
export function getAnnouncementTitleOnly(params: object) {
  return request({
    url: '/announcement/check-title-only',
    params
  })
}

// 职位审核详情列表
export function getJobDetailList(params: object) {
  return request({
    url: '/announcement/job-audit-detail-list',
    params
  })
}

// 公告排序
export function announcementSortChange(data: object) {
  return request({
    url: '/announcement/change-home-sort',
    method: 'post',
    data
  })
}

// 公告搜索可关联的活动
export function hwActivityList(data: object) {
  return request({
    url: '/hw-activity/search-announcement-hw-activity',
    method: 'post',
    data
  })
}

// 获取前后对比
export function getModifyCompare(params: object) {
  return request({
    url: '/announcement/get-modify-compare',
    params
  })
}
