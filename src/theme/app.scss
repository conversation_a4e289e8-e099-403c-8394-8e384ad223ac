/* 初始化样式
------------------------------- */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none !important;
}

html,
body,
#app {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: PingFang SC, Helvetica Neue, Helvetica, Hiragino Sans GB, Microsoft YaHei, SimSun,
    sans-serif;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -webkit-tap-highlight-color: transparent;
  background-color: #f8f8f8;
  font-size: 14px;
  // overflow: hidden;
  position: relative;

  .el-table__body {
    width: 100%;
    // 使表格兼容safari，不错位
    table-layout: fixed !important;
  }
}

/* 主布局样式
------------------------------- */
.layout-container {
  width: 100%;
  height: 100%;

  .layout-aside {
    background: var(--bg-menuBar);
    box-shadow: 2px 0 6px rgb(0 21 41 / 1%);
    height: inherit;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden !important;

    .el-scrollbar__view {
      overflow: hidden;
    }
  }

  .layout-header {
    padding: 0 !important;
  }

  .layout-main {
    padding: 0 !important;
    overflow: hidden;
    width: 100%;
    background-color: #f2f2f2;
  }

  .el-scrollbar {
    width: 100%;
  }

  // 此字段多次用到，建议不删除，如需修改，请重写覆盖样式
  .layout-view-bg-white {
    background: white;
    width: 100%;
    height: 100%;
    border-radius: 4px;
    border: 1px solid #ebeef5;
  }

  .layout-el-aside-br-color {
    border-right: 1px solid rgb(238, 238, 238);
  }

  .layout-aside-width-default {
    width: 220px !important;
    transition: width 0.3s ease;
  }

  .layout-aside-width64 {
    width: 64px !important;
    transition: width 0.3s ease;
  }

  .layout-aside-width1 {
    width: 1px !important;
    transition: width 0.3s ease;
  }

  .layout-scrollbar {
    @extend .el-scrollbar;
    padding: 15px;
  }

  .layout-mian-height-50 {
    height: calc(100vh - 50px);
  }

  .layout-columns-warp {
    flex: 1;
    display: flex;
    overflow: hidden;
  }

  .layout-hide {
    display: none;
  }
}

/* element plus 全局样式
------------------------------- */
.el-form--inline {
  .el-form-item {
    .el-input,
    .el-cascader,
    .el-select,
    .el-autocomplete {
      width: 240px;
    }
  }
}

.layout-breadcrumb-seting {
  .el-drawer__header {
    padding: 0 15px !important;
    height: 50px;
    display: flex;
    align-items: center;
    margin-bottom: 0 !important;
    border-bottom: 1px solid rgb(230, 230, 230);
  }

  .el-divider {
    background-color: rgb(230, 230, 230);
  }
}

/* nprogress 进度条跟随主题颜色
------------------------------- */
#nprogress {
  .bar {
    background: var(--color-primary) !important;
    z-index: 9999999 !important;
  }
}

/* flex 弹性布局
------------------------------- */
.flex {
  display: flex;
}

.flex-auto {
  flex: 1;
}

.flex-center {
  @extend .flex;
  flex-direction: column;
  width: 100%;
  overflow: hidden;
}

.flex-margin {
  margin: auto;
}

.flex-warp {
  display: flex;
  flex-wrap: wrap;
  align-content: flex-start;
  margin: 0 -5px;

  .flex-warp-item {
    padding: 5px;

    .flex-warp-item-box {
      width: 100%;
      height: 100%;
    }
  }
}

/* 宽高 100%
------------------------------- */
.w100 {
  width: 100% !important;
}

.h100 {
  height: 100% !important;
}

.vh100 {
  height: 100vh !important;
}

.max100vh {
  max-height: 100vh !important;
}

.min100vh {
  min-height: 100vh !important;
}

/* 颜色值
------------------------------- */
.color-primary {
  color: var(--color-primary);
}

.color-success {
  color: var(--color-success);
}

.color-warning {
  color: var(--color-warning);
}

.color-danger {
  color: var(--color-danger);
}

.color-info {
  color: var(--color-info);
}

.color-white {
  color: #fff;
}

.color-black {
  color: #000;
}

.color-dark {
  color: #333;
}

/* 字体大小全局样式
------------------------------- */
@for $i from 10 through 32 {
  .font#{$i} {
    font-size: #{$i}px !important;
  }
}

/* 外边距、内边距全局样式
------------------------------- */
@for $i from 1 through 35 {
  .mt#{$i} {
    margin-top: #{$i}px !important;
  }

  .mr#{$i} {
    margin-right: #{$i}px !important;
  }

  .mb#{$i} {
    margin-bottom: #{$i}px !important;
  }

  .ml#{$i} {
    margin-left: #{$i}px !important;
  }

  .pt#{$i} {
    padding-top: #{$i}px !important;
  }

  .pr#{$i} {
    padding-right: #{$i}px !important;
  }

  .pb#{$i} {
    padding-bottom: #{$i}px !important;
  }

  .pl#{$i} {
    padding-left: #{$i}px !important;
  }
}

/*
 * Author@DU 🔽
*/
/* 行高
------------------------------- */
.line-1 {
  line-height: 1;
}

/* 背景颜色
------------------------------- */
.bg-primary {
  color: var(--color-primary);
}

.bg-success {
  color: var(--color-success);
}

.bg-warning {
  color: var(--color-warning);
}

.bg-danger {
  color: var(--color-danger);
}

.bg-info {
  color: var(--color-info);
}

/* 字体粗细
------------------------------- */
@each $var in lighter, normal, bold, bolder {
  .fw-#{$var} {
    font-weight: $var;
  }
}

/* 字体对齐方式
------------------------------- */
@each $var in left, center, right {
  .ta-#{$var} {
    text-align: $var;
  }
}

/* 超出多少行隐藏 默认1-3行
------------------------------- */
@for $i from 1 through 3 {
  .to-#{$i} {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    line-clamp: $i;
    word-break: break-all;
    -webkit-line-clamp: $i;
    -webkit-box-orient: vertical;
  }
}

/* 文本下划线表现形式
------------------------------- */
@each $var in overline, line-through, underline, none {
  .td-#{$var} {
    text-decoration: $var;
  }
}

/* 滚动形式
------------------------------- */
@each $var in hidden, scroll {
  .#{$var} {
    overflow: $var;
  }

  .#{$var}-x {
    overflow-x: $var;
  }

  .#{$var}-y {
    overflow-y: $var;
  }
}

/* 盒子模型
------------------------------- */
@each $var in flex, block, inline, none, inline-block, inline-flex {
  .#{$var} {
    display: $var;
  }
}

/* 透明度
------------------------------- */
@each $short, $long in 0 0, 0.2 20, 0.4 40, 0.5 50, 0.6 60, 0.7 70, 0.8 80, 0.9 90, 1 100 {
  .opacity-#{$long} {
    opacity: $short;
  }
}

/* 定位形式
------------------------------- */
@each $var in relative, absolute, fixed, static, sticky {
  .p-#{$var} {
    position: $var;
  }
}

/* 通过定位居中
------------------------------- */
.p-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* 圆形
------------------------------- */
.circle {
  border-radius: 50vh;
}

/* flex布局
------------------------------- */
@each $var in column, row, column-reverse, row-reverse {
  .#{$var} {
    @extend .flex;
    flex-direction: $var;
  }
}

@each $short, $long in start flex-start, end flex-end, center center, between space-between,
  around space-around
{
  .jc-#{$short} {
    @extend .flex;
    justify-content: $long;
  }
}

@each $short, $long in start flex-start, center center, end flex-end, stretch stretch,
  baseline baseline
{
  .ai-#{$short} {
    @extend .flex;
    align-items: $long;
  }
}

@each $short, $long in start flex-start, center center, end flex-end, stretch stretch,
  between space-between, around space-around
{
  .ac-#{$short} {
    @extend .flex;
    align-content: $long;
  }
}

@each $var in wrap, nowrap, wrap-reverse {
  .#{$var} {
    display: flex;
    flex-wrap: $var;
  }
}

.shrink-0 {
  flex-shrink: 0;
}

@each $short, $long in start flex-start, center center, end flex-end, stretch stretch,
  baseline baseline, auto auto
{
  .as-#{$short} {
    align-self: $long;
  }
}

/* 水平垂直居中
------------------------------- */
.center {
  @extend .flex;
  justify-content: center;
  align-items: center;
  align-content: center;
}

/* 剩余空间充满方式
------------------------------- */
@each $i in (1, 2, 3, 4, 5) {
  .flex-#{$i} {
    flex: $i;
    flex-shrink: 0;
  }
}

/* basis 宽度
------------------------------- */
@for $i from 1 through 10 {
  .span-#{$i} {
    flex-basis: calc(100% / $i);
  }
}

/* 边距
------------------------------- */
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

@each $i
  in (
    0,
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    22,
    24,
    26,
    28,
    30,
    32,
    34,
    36,
    38,
    40,
    42,
    44,
    46,
    48,
    50,
    60,
    70,
    80,
    90,
    100
  )
{
  .m-#{$i} {
    margin: $i + px;
  }

  .my-#{$i} {
    margin-top: $i + px;
    margin-bottom: $i + px;
  }

  .mx-#{$i} {
    margin-right: $i + px;
    margin-left: $i + px;
  }

  .p-#{$i} {
    padding: $i + px;
  }

  .py-#{$i} {
    padding-top: $i + px;
    padding-bottom: $i + px;
  }

  .px-#{$i} {
    padding-right: $i + px;
    padding-left: $i + px;
  }

  @each $short, $long in l left, t top, r right, b bottom {
    .m#{$short}-#{$i} {
      margin-#{$long}: $i + px !important;
    }

    .p#{$short}-#{$i} {
      padding-#{$long}: $i + px !important;
    }
  }
}

/* 字体大小
------------------------------- */
@each $i
  in (
    8,
    9,
    10,
    11,
    12,
    13,
    14,
    15,
    16,
    17,
    18,
    19,
    20,
    22,
    24,
    26,
    28,
    30,
    32,
    34,
    36,
    38,
    40,
    42,
    44,
    46,
    48,
    50
  )
{
  .fs-#{$i} {
    font-size: $i + px;
  }
}

/* 宽度或高度
------------------------------- */
@each $i
  in (
    10,
    12,
    14,
    16,
    18,
    20,
    22,
    24,
    26,
    28,
    30,
    32,
    34,
    36,
    38,
    40,
    42,
    44,
    46,
    48,
    50,
    55,
    60,
    65,
    70,
    75,
    80,
    85,
    90,
    95,
    100,
    110,
    120,
    130,
    140,
    150,
    160,
    170,
    180,
    190,
    200,
    250,
    300
  )
{
  .w-#{$i} {
    width: $i + px;
  }

  .h-#{$i} {
    height: $i + px;
  }
}

/* 鼠标类型
------------------------------- */
@each $var in auto, default, pointer, move, text, wait, help {
  .cursor-#{$var} {
    cursor: $var;
  }
}

/* 禁止选中文本
------------------------------- */
.select-none {
  user-select: none;
}

/* 鼠标点击
------------------------------- */
.point {
  cursor: pointer;
}

@mixin utils-ellipsis-lines($line-size, $line-height, $font-size: 14px) {
  @if (unitless($font-size)) {
    @error '$font-size must be a unit number';
  }

  $line-height: if(unitless($line-height), $font-size * $line-height, $line-height);
  $max-height: $line-height * $line-size;

  display: -webkit-box;
  -webkit-line-clamp: $line-size;
  -webkit-box-orient: vertical;
  max-height: $max-height;
  font-size: $font-size;
  line-height: $line-height;
  word-break: break-word;
  white-space: normal;
  text-overflow: ellipsis;
  overflow: hidden;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.hide {
  display: none !important;
}

.show {
  display: block !important;
}

/*全局样式*/
.fl {
  float: left !important;
}

.fr {
  float: right !important;
}

.tc {
  text-align: center;
}

.clearfix:after,
.clearfix:before {
  content: '.'; /*内容为“.”就是一个英文的句号而已。也可以不写。*/
  display: block; /*加入的这个元素转换为块级元素。*/
  clear: both; /*清除左右两边浮动。*/
  visibility: hidden; /*可见度设为隐藏。注意它和display:none;是有区别的。visibility:hidden;仍然占据空间，只是看不到而已；*/
  line-height: 0; /*行高为0；*/
  height: 0; /*高度为0；*/
  font-size: 0; /*字体大小为0；*/
}

.clearfix {
  *zoom: 1;
}

.color-black {
  color: black;
}

.color-blue {
  color: #2db7f5;
}

.color-green {
  color: green;
}

.color-red {
  color: red;
}

.color-yellow {
  color: #ff9900;
}

.cp {
  cursor: pointer;
}

.flex-cont {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}

.flex1 {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  -moz-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
}

.flex-centerbox {
  /*水平居中*/
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  /*垂直居中*/
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.flex-center-y {
  -webkit-box-align: center;
  -webkit-align-items: center;
  align-items: center;
}

.flex-center-x {
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}
