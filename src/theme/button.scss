@import './common/color.scss';

.table-button-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 8px;
  row-gap: 8px;

  .el-button {
    & + .el-button {
      margin-left: 0;
    }

    @each $key, $value in $btn-colors {
      &.#{'' + $key} {
        $color: map-get(
          $map: $value,
          $key: color
        );
        $bg: map-get(
          $map: $value,
          $key: background
        );
        $border-color: map-get(
          $map: $value,
          $key: border-color
        );

        color: $color;
        background: $bg;
        border: 1px solid $border-color;
        width: 72px;

        &.is-disabled {
          color: $color;
          background: rgba($color: $bg, $alpha: 0.4);
          border: 1px solid rgba($color: $border-color, $alpha: 0);

          &:hover {
            color: $color;
            background: rgba($color: $bg, $alpha: 0.4);
            border: 1px solid rgba($color: $border-color, $alpha: 0);
          }
        }
      }
    }
  }
}

.table-popover-button {
  display: flex;
  flex-direction: column;

  .el-button {
    & + .el-button {
      margin-left: 0;
      margin-top: 8px;
    }
  }
}
