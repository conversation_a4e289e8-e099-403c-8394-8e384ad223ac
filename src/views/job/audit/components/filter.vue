<template>
  <div class="filter-container-template">
    <el-form ref="form" class="filter-grid-6" :model="formData">
      <el-form-item label="职位检索" prop="jobName">
        <el-input
          v-model="formData.jobName"
          placeholder="请填写职位名称或编号"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="单位检索" prop="companyName" @keyup.enter="handleSearch" clearable>
        <el-input v-model="formData.companyName" placeholder="请填写单位名称或编号"></el-input>
      </el-form-item>
      <el-form-item label="申 请 人" prop="applyAdminName">
        <el-input
          v-model="formData.applyAdminName"
          placeholder="请填写申请人"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="申请时间" prop="applyAuditTimeStart">
        <DatePickerRange
          v-model:start="formData.applyAuditTimeStart"
          v-model:end="formData.applyAuditTimeEnd"
        />
      </el-form-item>
    </el-form>

    <div class="filter-tools">
      <div class="filter-btn-group">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleResetField">重置</el-button>
        <el-button @click="handleDownload">下载</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'

const emit = defineEmits(['search', 'download'])
const form = ref()

const formData = reactive({
  jobName: '',
  companyName: '',
  applyAuditTimeStart: '',
  applyAuditTimeEnd: '',
  applyAdminName: '',
  export: 0
})

onMounted(() => {
  emit('search', formData)
})

const handleResetField = () => {
  form.value.resetFields()
  emit('search', formData)
}

const handleDownload = () => {
  formData.export = 1
  emit('search', formData)
}

const handleSearch = () => {
  formData.export = 0
  emit('search', formData)
}
</script>

<style scoped lang="scss">
:deep(.el-form-item__label) {
  padding-right: 5px !important;
}
.el-button--small {
  padding: 9px 10px;
}
</style>
