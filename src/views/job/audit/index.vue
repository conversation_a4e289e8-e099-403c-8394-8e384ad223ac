<template>
  <div class="box">
    <div id="top-container" class="pb-20">
      <Filter @search="handleSearch" />

      <div class="amount">
        共计：<span class="danger">{{ pagination.total }}</span
        >个职位
      </div>
    </div>

    <el-table
      :data="list"
      border
      v-loading="loading"
      @sort-change="handleSortable"
      :max-height="maxTableHeight"
    >
      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
        >
          <template v-if="item.slot === 'jobName'" #default="{ row }">
            <el-link
              class="fw-normal fs-12"
              :underline="false"
              @click="handleJobDetail(row.jobId)"
              >{{ row.jobName }}</el-link
            >
          </template>

          <template v-else-if="item.slot === 'baseInfo'" #default="{ row }">
            {{ row.city }} | {{ row.educationTypeTitle }}
            {{ row.experienceType ? ' | ' + row.experienceType : '' }}
            {{ row.wage ? ' | ' + row.wage : '' }}
          </template>

          <template v-else-if="item.slot === 'companyName'" #default="{ row }">
            <router-link :to="`/company/details?id=${row.companyId}`">{{
              row.companyName
            }}</router-link>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <el-button @click="openAuditDetail(row.jobId)" size="small" type="primary"
              >审核</el-button
            >
          </template>
        </el-table-column>
      </template>

      <template #empty>
        <el-empty description="暂无数据"></el-empty>
      </template>
    </el-table>

    <div id="bottom-container" v-show="pagination.total > 0" class="jc-end" style="flex-shrink: 0">
      <Pagination
        v-if="list.length"
        class="pt-15"
        :total="pagination.total"
        @change="handlePaginationChange"
      />
    </div>
  </div>
  <AuditDialog ref="auditDialog" />
  <JobDetail ref="jobDetail" />
</template>

<script setup lang="ts">
import { ref, reactive, onActivated, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import Filter from './components/filter.vue'
import Pagination from '/@/components/base/paging.vue'
import AuditDialog from './components/auditDialog.vue'
import JobDetail from '/@/components/job/jobDetailDialog.vue'
import { getJobAuditList, getUnJobAuditList } from '/@/api/job'

interface FormData {
  sortName: string
  sortReleaseTime: string
  sortApplyTime: string
  export: number
  page: number
  limit: number
  [key: string]: string | number
}

const route = useRoute()

const auditDialog = ref()
const router = useRouter()
const jobDetail = ref()

const loading = ref(false)
const maxTableHeight = ref(450)

const customColumns = ref([
  {
    prop: 'jobUuid',
    key: 'id',
    label: '职位ID',
    select: true,
    default: true
  },
  {
    prop: 'jobName',
    key: 'jobName',
    label: '职位名称',
    slot: 'jobName',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'baseInfo',
    key: 'baseInfo',
    label: '基本信息',
    slot: 'baseInfo',
    select: true,
    default: true
  },
  {
    prop: 'companyName',
    key: 'companyName',
    label: '所属单位',
    slot: 'companyName',
    select: true,
    default: true
  },
  {
    prop: 'status',
    key: 'status',
    label: '招聘状态',
    select: true,
    default: true
  },
  {
    prop: 'applyAdminName',
    key: 'applyAdminName',
    label: '申请人',
    select: true,
    default: true
  },
  {
    prop: 'creator',
    key: 'creator',
    label: '创建人',
    select: true,
    default: true
  },
  {
    prop: 'addTime',
    key: 'addTime',
    label: '创建时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'applyAuditTime',
    key: 'applyAuditTime',
    label: '申请审核时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'publishTime',
    key: 'publishTime',
    label: '发布时间',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'operation',
    key: 'operation',
    label: '操作',
    slot: 'operation',
    select: true,
    default: true
  }
])

const formData = reactive<FormData>({
  sortName: '',
  sortReleaseTime: '',
  sortApplyTime: '',
  export: 0,
  page: 1,
  limit: 20
})

const pagination = reactive({
  total: 0
})

const list = ref([])

const setColumnMinWidth = (key: string) => {
  let minWidth = 90

  switch (key) {
    case 'addTime':
      minWidth = 170
      break
    case 'applyAuditTime':
      minWidth = 170
      break
    case 'publishTime':
      minWidth = 170
      break

    default:
      minWidth = 90
      break
  }

  return minWidth
}

const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

const getList = async (sort = {}) => {
  loading.value = true

  const isCooperation = route.path === '/job/audit'
  const getListFetch = isCooperation ? getJobAuditList : getUnJobAuditList
  await getListFetch({ ...formData, ...sort }).then((resp: any) => {
    if (formData.export === 1) {
      const aEl = document.createElement('a')
      aEl.setAttribute('href', resp.excelUrl)
      aEl.click()
    } else {
      list.value = resp.list
      pagination.total = Number(resp.page.count)
    }
  })
  loading.value = false

  getTableHeight()
}

onActivated(() => {
  getList()
})

const handleSearch = (filter: Partial<FormData>) => {
  Object.assign(formData, filter)
  getList()
}

const handlePaginationChange = (data: { page: number; limit: number }) => {
  formData.page = data.page
  formData.limit = data.limit
  getList()
}

const handleSortable = ({ prop, order }: { prop: string; order: string }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sortMap = {
    applyAuditTime: 'sortApplyAuditTime',
    addTime: 'sortAddTime',
    refreshTime: 'sortRefreshTime'
  }

  const key = sortMap[prop]
  const sort = order === 'ascending' ? 2 : 1

  getList({ [key]: sort })
}

const openAuditDetail = (id: string) => {
  router.push(`/job/audit/detail/${id}`)
}

const handleJobDetail = (id: string) => {
  jobDetail.value.open(id)
}
</script>

<script lang="ts">
export default {
  name: 'jobAudit'
}
</script>

<style scoped lang="scss">
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}
.amount {
  margin: 20px 0 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
</style>
