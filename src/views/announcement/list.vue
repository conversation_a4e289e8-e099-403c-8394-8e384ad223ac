<template>
  <div class="box">
    <div id="top-container" class="pb-20">
      <div class="filter-container-template">
        <el-form class="filter-grid-6" ref="announcementForm" :model="form">
          <el-form-item label="公告检索" prop="announcementTitleNum">
            <el-input
              placeholder="请填写公告标题或编号"
              v-model="form.announcementTitleNum"
              clearable
              @keyup.enter="getList"
            />
          </el-form-item>
          <el-form-item label="单位检索" prop="companyName">
            <el-input
              placeholder="请填写单位名称或编号"
              v-model="form.companyName"
              clearable
              @keyup.enter="getList"
            />
          </el-form-item>
          <el-form-item label="招聘状态" prop="status">
            <el-select v-model="form.status" placeholder="不限" filterable clearable>
              <el-option
                v-for="item in statusRecruitList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="初发时间" prop="firstReleaseTimeStart" label-width="100px">
            <DatePickerRange
              v-model:start="form.firstReleaseTimeStart"
              v-model:end="form.firstReleaseTimeEnd"
            />
          </el-form-item>
          <el-form-item label="刷新时间" prop="realRefreshTimeStart">
            <DatePickerRange
              v-model:start="form.realRefreshTimeStart"
              v-model:end="form.realRefreshTimeEnd"
            />
          </el-form-item>
          <el-form-item label="发布时间" prop="refreshTimeStart">
            <DatePickerRange
              v-model:start="form.refreshTimeStart"
              v-model:end="form.refreshTimeEnd"
            />
          </el-form-item>

          <div class="fill-template" v-show="showMore">
            <el-form-item label="创建人" prop="creator">
              <el-input
                placeholder="请填写创建人账号"
                v-model="form.creator"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item label="所在栏目" prop="columnId">
              <Colunm v-model="form.columnId" :columnList="columnList" multiple :check="false" />
            </el-form-item>
            <el-form-item label="公告属性" prop="notOverseasAttribute">
              <el-select
                v-model="form.notOverseasAttribute"
                placeholder="不限"
                clearable
                filterable
              >
                <el-option
                  v-for="item in notOverseasAttributeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="投递方式" prop="deliveryWay">
              <ResumeSource v-model="form.deliveryWay" multiple />
            </el-form-item>
            <el-form-item label="职位检索" prop="name">
              <el-input
                placeholder="请填写职位名称或编号"
                v-model="form.name"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item label="职位类型" prop="jobCategoryId">
              <JobCategory v-model="form.jobCategoryId" />
            </el-form-item>
            <el-form-item label="学历要求" prop="educationType">
              <Education v-model="form.educationType" />
            </el-form-item>
            <el-form-item label="需求专业" prop="majorId">
              <MajorCategory v-model="form.majorId" :multiple="false" :deep="2" />
            </el-form-item>
            <el-form-item label="工作地点" prop="city">
              <Region v-model="form.city" />
            </el-form-item>
            <el-form-item label="用人部门" prop="department">
              <el-input
                placeholder="请填写用人部门"
                v-model="form.department"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item label="工作性质" prop="natureType">
              <WorkNature v-model="form.natureType" />
            </el-form-item>
            <el-form-item label="海外经历" prop="abroadType">
              <AbroadExperience ispageSize v-model="form.abroadType" />
            </el-form-item>
            <el-form-item label="工作经验" prop="experienceType">
              <WorkExperience v-model="form.experienceType" />
            </el-form-item>
            <el-form-item label="职称要求" prop="titleType">
              <LevelTitle v-model="form.titleType" />
            </el-form-item>
            <el-form-item label="政治面貌" prop="politicalType">
              <Political v-model="form.politicalType" />
            </el-form-item>
            <el-form-item label="审核人" prop="">
              <el-input
                placeholder="请填写审核人"
                v-model="form.auditAdminName"
                clearable
                @keyup.enter="getList"
              />
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="form.auditStatus" placeholder="不限" clearable filterable>
                <el-option
                  v-for="item in auditStatusList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否小程序" prop="isMiniapp">
              <IsMiniapp v-model="form.isMiniapp" />
            </el-form-item>
            <el-form-item label="所属主栏目" prop="homeColumnId">
              <Colunm
                v-model="form.homeColumnId"
                :columnList="columnList"
                multiple
                :check="false"
              />
            </el-form-item>
            <el-form-item label="编制类型" prop="establishmentType">
              <EstablishmentType v-model="form.establishmentType" />
            </el-form-item>
            <el-form-item label="单位类型" prop="companyType">
              <CompanyType multiple v-model="form.companyType" />
            </el-form-item>
            <el-form-item label="单位性质" prop="companyNature">
              <CompanyNature multiple v-model="form.companyNature" />
            </el-form-item>
            <el-form-item label="显示状态" prop="isShow">
              <el-select v-model="form.isShow" placeholder="请选择" clearable>
                <el-option label="-" :value="-1"> </el-option>
                <el-option label="显示" :value="1"> </el-option>
                <el-option label="隐藏" :value="2"> </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="年龄要求" prop="ageType">
              <Age v-model="form.ageType" @keyup.enter="getList" />
            </el-form-item>

            <!-- <el-form-item label="高才海外相关属性" prop="overseasAttribute">
              <el-select v-model="form.overseasAttribute" placeholder="不限" clearable filterable>
                <el-option
                  v-for="item in overseasAttributeList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item> -->
          </div>
        </el-form>

        <div class="filter-tools">
          <!-- <div class="filter-tools-left">
            <slot name="left">
              <el-switch
                v-model="isSimple"
                size="large"
                active-text="简版(只支持部分搜索条件和显示结果)"
                inactive-text="全功能"
              />
            </slot>
          </div> -->

          <div class="filter-btn-group">
            <slot name="right">
              <el-button type="primary" @click="getList">搜索</el-button>
              <el-button @click="reset">重置</el-button>
              <el-button @click="downloadExcel">下载</el-button>
              <!-- <el-button type="primary" link @click="showMore = !showMore">更多</el-button> -->
              <el-link :underline="false" class="show" @click="handleShowMore">{{
                showMore ? '收起' : '展开'
              }}</el-link>
            </slot>
          </div>
        </div>
      </div>

      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ pagination.total }}</span>
          则公告
        </div>
        <el-link :underline="false" type="primary" size="small" @click="handleOpenCustomColumn"
          >选择列</el-link
        >
      </div>
    </div>

    <el-table
      :data="announcementList"
      :max-height="maxTableHeight"
      border
      @selection-change="handleAnnounceChange"
      v-loading="loading"
      ref="announcementTable"
      @sort-change="handleSortTable"
    >
      <el-table-column type="selection" width="40" />

      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
          :width="item.key === 'operation' ? '256px' : 'auto'"
        >
          <template v-if="item.headerSlot === 'sort'" #header>
            {{ item.label }}
            <el-tooltip
              class="box-item"
              effect="dark"
              content="此排序仅对单位主页中的公告排序生效"
              placement="top"
            >
              <el-icon><QuestionFilled color="#909399" /></el-icon>
            </el-tooltip>
          </template>

          <template v-if="item.slot === 'title'" #default="{ row }">
            <router-link
              class="bg-primary td-none"
              :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
              >{{ row.title }}</router-link
            >
            <p class="tip danger">
              {{ row.tip }}
            </p>
          </template>

          <template v-else-if="item.slot === 'companyName'" #default="{ row }">
            <el-button type="primary" link @click="toCompanyDetail(row.companyId)">
              {{ row.fullName }}
            </el-button>
          </template>

          <template v-else-if="item.slot === 'sort'" #default="{ row }">
            <div class="sort ai-center">
              {{ row.homeSort }}
              <img
                @click="handleChangeSort(row)"
                class="sort-edit"
                src="/src/assets/icons/edit.svg"
                alt=""
              />
            </div>
          </template>

          <template v-else-if="item.slot === 'jobNum'" #default="{ row }">
            <router-link
              :to="{ path: '/job/query', query: { id: row.announcementUid } }"
              class="bg-primary td-none"
              v-if="row.jobNum !== '0'"
              >{{ row.jobNum }}</router-link
            >
            <div v-else class="bg-primary">{{ row.jobNum }}</div>
          </template>

          <template v-else-if="item.slot === 'onlineCount'" #default="{ row }">
            <router-link
              :to="{ path: '/job/query', query: { id: row.announcementUid } }"
              class="bg-primary td-none"
              v-if="row.onlineCount !== '0'"
              >{{ row.onlineCount }}</router-link
            >
            <div v-else class="bg-primary">{{ row.onlineCount }}</div>
          </template>

          <template v-else-if="item.slot === 'platformNum'" #default="{ row }">
            <router-link
              :to="{ path: '/announcement/business', query: { id: row.aid } }"
              class="bg-primary td-none"
              v-if="row.platformNum !== '0'"
              >{{ row.platformNum }}</router-link
            >
            <div v-else class="bg-primary">
              {{ row.platformNum }}
            </div>
          </template>

          <template v-else-if="item.slot === 'emailNum'" #default="{ row }">
            <router-link
              :to="{ path: '/announcement/business', query: { id: row.aid } }"
              class="bg-primary td-none"
              v-if="row.emailNum !== '0'"
              >{{ row.emailNum }}</router-link
            >
            <div v-else class="bg-primary">
              {{ row.emailNum }}
            </div>
          </template>

          <template v-else-if="item.slot === 'webNum'" #default="{ row }">
            <router-link
              :to="{ path: '/announcement/business', query: { id: row.aid, active: 2 } }"
              class="bg-primary td-none"
              v-if="row.linkNum !== '0'"
              >{{ row.linkNum }}</router-link
            >
            <span class="bg-primary" v-else>
              {{ row.linkNum }}
            </span>
          </template>

          <template v-else-if="item.slot === 'isMiniapp'" #default="{ row }">
            <isMiniappChange
              :value="row.isMiniapp"
              type="announcement"
              :id="row.aid"
            ></isMiniappChange>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <div class="table-button-group">
              <template v-if="row.btnList?.length < 7">
                <template v-if="item.key !== 'attribute'">
                  <el-button
                    v-for="item in row.btnList"
                    :key="item.key"
                    size="small"
                    :disabled="item.disabled === 2"
                    :class="item.class"
                    @click="btnGroupEvent(item.key, row)"
                    >{{ item.label }}
                  </el-button>
                </template>

                <template v-else>
                  <el-popover placement="left" :width="20" trigger="click">
                    <template #reference>
                      <el-button
                        @click="getAttributeData(row.articleId)"
                        class="w100 mx-0 my-5"
                        plain
                        size="small"
                        >{{ item.label }}</el-button
                      >
                    </template>
                    <div class="column btns">
                      <el-button
                        class="w100 mx-0 my-5 ml-0"
                        plain
                        size="small"
                        v-for="item in attributeData"
                        :key="item.type"
                        @click="refreshSortTime(item.type, item.articleId)"
                        >{{ item.typeTxt }}</el-button
                      >
                    </div>
                  </el-popover>
                </template>
              </template>

              <template v-else>
                <el-button
                  v-for="item in row.btnList?.slice(0, 5)"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>

                <template v-if="row.btnList?.slice(5).length">
                  <el-popover placement="left" width="auto" trigger="click">
                    <template #reference>
                      <el-button class="white" size="small">更多</el-button>
                    </template>

                    <div class="table-popover-button">
                      <template v-for="item in row.btnList?.slice(5)">
                        <template v-if="item.key !== 'attribute'">
                          <el-button
                            :key="item.key"
                            size="small"
                            :disabled="item.disabled === 2"
                            :class="item.class"
                            @click="btnGroupEvent(item.key, row)"
                            >{{ item.label }}
                          </el-button>
                        </template>

                        <template v-else>
                          <el-popover :key="item.key" placement="left" :width="20" trigger="click">
                            <template #reference>
                              <el-button
                                @click="getAttributeData(row.announcementId)"
                                class="w100 mx-0 my-5"
                                plain
                                size="small"
                                >{{ item.label }}</el-button
                              >
                            </template>
                            <div class="column btns">
                              <el-button
                                class="w100 mx-0 my-5 ml-0"
                                plain
                                size="small"
                                v-for="item in attributeData"
                                :key="item.type"
                                @click="refreshSortTime(item.type, item.articleId)"
                                >{{ item.typeTxt }}</el-button
                              >
                            </div>
                          </el-popover>
                        </template>
                      </template>
                    </div>
                  </el-popover>
                </template>
              </template>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            v-model="checkAll"
            label="全选"
            class="mr-10"
            @change="announcementChange"
            :indeterminate="isIndeterminate"
          ></el-checkbox>

          <el-select
            v-model="batchValue"
            placeholder="批量操作"
            :disabled="!announceSelection.length"
            @change="announceSelectBatch"
            clearable
            filterable
          >
            <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k" />
          </el-select>
        </div>
      </div>
      <Paging class="mt-15" @change="handlePaginationChange" :total="pagination.total" />
    </div>
  </div>
  <CustomColumnDialog ref="customColumnDialog" v-model:data="customColumns" />
  <AnnouncementLogDialog ref="logDialog" />
  <AnnouncementEdit ref="announcementEdit" :list="attributeDocument" />
  <AnnouncementBatchCopy ref="announcementBatchCopy" />
  <SortChangeDialog ref="sortChangeDialog" />
</template>

<script setup lang="ts">
import { ElMessage, ElMessageBox } from 'element-plus'
import { QuestionFilled } from '@element-plus/icons-vue'

import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import AnnouncementEdit from '../cms/announcement/component/announcementEdit.vue'
import {
  announcementBatchOnline,
  announcementBatchRefresh,
  announcementDelete,
  announcementIsShow,
  announcementIsHide,
  announcementOffLine,
  announcementRepublish,
  announcementRefresh,
  announcementRefreshSortTime,
  getAnnouncementAttributeData,
  getAnnouncementList,
  getAnnounceSearchParams,
  batchHidden,
  batchShow,
  getSimpleAnnouncementList
} from '/@/api/announcement'
import { getTableStagingField } from '/@/api/config'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Paging from '/@/components/base/paging.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import Education from '/@select/education.vue'
import JobCategory from '/@select/jobCategory.vue'
import LevelTitle from '/@select/levelTitle.vue'
import ResumeSource from '/@select/resumeSource.vue'
import MajorCategory from '/@select/majorCategory.vue'
import Political from '/@select/political.vue'
import Region from '/@select/region.vue'
import WorkExperience from '/@select/workExperience.vue'
import WorkNature from '/@select/workNature.vue'
import Age from '/@select/age.vue'
import CustomColumnDialog from '/@/components/business/customColumnDialog-V2.vue'
import AnnouncementBatchCopy from '/@/components/dialog/announcementBatchCopy.vue'
import AnnouncementLogDialog from '/@/views/announcement/components/announcementLogDialog.vue'
import Colunm from '/@/components/base/colunm.vue'
import SortChangeDialog from '/src/components/business/sortChangeDialog.vue'
import IsMiniapp from '/@select/isMiniapp.vue'
import isMiniappChange from '/@select/isMiniappChange.vue'
import EstablishmentType from '/@select/establishmentType.vue'
import CompanyType from '/@select/companyType.vue'
import CompanyNature from '/@select/companyNature.vue'

const form = ref({
  isCooperation: 1,
  announcementTitleNum: '',
  name: '',
  companyName: '',
  jobCategoryId: '',
  educationType: '',
  city: [],
  status: '',
  deliveryWay: [],
  majorId: '',
  auditAdminName: '',
  establishmentType: '',
  companyType: [],
  companyNature: [],
  firstReleaseTimeStart: '',
  firstReleaseTimeEnd: '',
  refreshTimeStart: '',
  refreshTimeEnd: '',
  releaseTimeStart: '',
  releaseTimeEnd: '',
  realRefreshTimeStart: '',
  realRefreshTimeEnd: '',
  department: '',
  natureType: '',
  abroadType: '',
  experienceType: '',
  titleType: '',
  ageType: '',
  politicalType: '',
  auditStatus: '',
  creator: '',
  overseasAttribute: '',
  columnId: '',
  notOverseasAttribute: '',
  isMiniapp: '',
  homeColumnId: '',
  isShow: ''
})

const pagination = ref({
  total: 0,
  pageSize: 20,
  page: 1
})

const announcementList = ref([])

const amount = <any>ref({})
const loading = ref(false)
const checkAll = ref(false)
const batchValue = ref('')
const batchOptions = ref([
  { k: 1, v: '再发布文档' },
  { k: 2, v: '下线文档' },
  { k: 3, v: '刷新文档' },
  { k: 4, v: '编辑属性' },
  // { k: 5, v: '复制文档' },
  // { k: 6, v: '移动文档' },
  { k: 7, v: '显示文档' },
  { k: 8, v: '隐藏文档' }
])
const announceSelection = ref([])
const attributeData = <any>ref([])

const maxTableHeight = ref(450)

const showMore = ref(false)
const isSimple = ref(false)

const overseasAttributeList = ref([])
const notOverseasAttributeList = <any>ref([])
const announceIds = ref('')
const attributeDocument = ref([])
const columnList = ref([])
const statusRecruitList = <any>ref([])
const auditStatusList = <any>ref([])

const customColumns = ref([
  {
    prop: 'announcementUid',
    key: 'announcementUid',
    label: '公告ID',
    select: true,
    default: true
  },
  {
    prop: 'title',
    key: 'title',
    label: '公告标题',
    slot: 'title',
    select: true,
    default: true
  },
  {
    prop: 'companyName',
    key: 'companyName',
    label: '所属单位',
    slot: 'companyName',
    select: true,
    default: true
  },
  {
    prop: 'amountCount',
    key: 'amount',
    label: '招聘人数',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'homeSort',
    key: 'sort',
    label: '排序',
    sortable: 'custom',
    slot: 'sort',
    headerSlot: 'sort',
    select: true,
    default: true
  },
  {
    prop: 'allJobAmount',
    key: 'jobNum',
    label: '招聘职位',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'onlineJobAmount',
    key: 'onlineCount',
    label: '在线职位',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'offlineJobAmount',
    key: 'offlineCount',
    label: '已下线职位',
    select: false,
    default: false
  },
  {
    prop: 'platformNum',
    key: 'platformNum',
    label: '平台投递',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'emailNum',
    key: 'emailNum',
    label: '邮箱投递',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'linkNum',
    key: 'webNum',
    label: '网址投递',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'amount',
    key: 'linkNum',
    label: '投递次数',
    select: true,
    default: true
  },
  {
    prop: 'click',
    key: 'click',
    label: '点击量',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'homeColumnTxt',
    key: 'column',
    label: '所属主栏目',
    select: false,
    default: false
  },
  {
    prop: 'allHomeColumnTxt',
    key: 'columnSub',
    label: '所属栏目',
    select: false,
    default: false
  },
  {
    prop: 'status',
    key: 'status',
    label: '招聘状态',
    select: false,
    default: false
  },
  {
    prop: 'auditStatusTxt',
    key: 'auditStatus',
    label: '审核状态',
    select: false,
    default: false
  },
  {
    prop: 'isShowTxt',
    key: 'isShow',
    label: '显示状态',
    select: false,
    default: false
  },
  {
    prop: 'addDate',
    key: 'addDate',
    label: '创建时间',
    select: false,
    default: false
  },
  {
    prop: 'firstReleaseTime',
    key: 'firstData',
    label: '初始发布时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'refreshTime',
    key: 'refreshTime',
    sortable: 'custom',
    label: '发布时间',
    select: false,
    default: false
  },
  {
    prop: 'realRefreshTime',
    key: 'realRefreshTime',
    label: '刷新时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'offlineDate',
    key: 'offlineDate',
    label: '下线时间',
    select: false,
    default: false
  },
  {
    prop: 'creatorName',
    key: 'creatorName',
    label: '创建人',
    select: false,
    default: false
  },
  {
    prop: 'audio',
    key: 'audio',
    label: '审核人',
    select: false,
    default: false
  },
  {
    prop: 'majorTxt',
    key: 'majorTxt',
    label: '学科专业',
    select: false,
    default: false
  },
  {
    prop: 'isMiniappTxt',
    key: 'isMiniappTxt',
    label: '是否小程序',
    select: false,
    default: false
  },
  {
    prop: 'operation',
    key: 'operation',
    label: '操作',
    slot: 'operation',
    disabled: true,
    select: true,
    default: true
  }
])

const customColumnDialog = ref()
const announcementTable = ref()
const announcementForm = ref()
const logDialog = ref()
const announcementEdit = ref()
const announcementBatchCopy = ref()
const router = useRouter()
const isIndeterminate = ref(false)
const sortChangeDialog = ref()

const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

const getList = async (sort = {}) => {
  loading.value = true

  const params = {}
  Object.keys(form.value).forEach((key: string) => {
    const value = form.value[key]
    params[key] = Array.isArray(value) ? value.join() : value
  })
  let rs: any = {}
  if (isSimple.value) {
    rs = await getSimpleAnnouncementList({ ...params, ...sort })
    rs.amount = {}
  } else {
    rs = await getAnnouncementList({ ...params, ...sort })
  }
  announcementList.value = rs.list
  amount.value = rs.amount
  pagination.value.total = rs.page.count
  loading.value = false
  getTableHeight()
}

onMounted(async () => {
  const {
    columnList: column,
    statusRecruitList: statusRecruit,
    auditStatusList: auditStatus,
    overseasAttributeList: overseasAttribute,
    notOverseasAttributeList: notOverseasAttribute
  } = await (<any>getAnnounceSearchParams())
  columnList.value = column
  statusRecruitList.value = statusRecruit
  overseasAttributeList.value = overseasAttribute
  notOverseasAttributeList.value = notOverseasAttribute
  auditStatusList.value = auditStatus

  getList()

  const res = await getTableStagingField('announcementQueryCooperativeUnitYes')
  if (!res.value) return
  const value = res.value.split(',')
  customColumns.value = customColumns.value.map((item: any) => {
    return {
      ...item,
      select: value.includes(item.key)
    }
  })
})

const handleOpenCustomColumn = () => {
  customColumnDialog.value.open('announcementQueryCooperativeUnitYes')
}

interface params {
  page: number
  limit: number
}

const handlePaginationChange = (data: params) => {
  pagination.value.page = data.page
  pagination.value.pageSize = data.limit
  getList()
}

const handleAnnounceChange = (data: any) => {
  announceSelection.value = data
  announceIds.value = data.map((item: any) => item.aid).join()
  if (data.length === announcementList.value.length) {
    checkAll.value = true
    isIndeterminate.value = false
  } else {
    checkAll.value = false
    isIndeterminate.value = data.length > 0
  }
}

const announcementChange = () => {
  announcementTable.value.toggleAllSelection()
}

const handleSortTable = ({ prop, order }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sortMap = {
    amountCount: 'sortAllJobAmount',
    onlineJobAmount: 'sortOnlineJobAmount',
    platformNum: 'sortPlatformNum',
    emailNum: 'sortEmailNum',
    linkNum: 'sortLinkNum',
    interviewNum: 'sortInterviewNum',
    click: 'sortClick',
    firstReleaseTime: 'sortFirstReleaseTime',
    refreshTime: 'sortRefreshTime',
    realRefreshTime: 'sortRealRefreshTime'
  }

  const key = sortMap[prop]
  const sort = order === 'ascending' ? 2 : 1

  getList({ [key]: sort })
}

const reset = () => {
  announcementForm.value.resetFields()
  getList()
}

const refreshAnnounce = (announcementId: string) => {
  ElMessageBox.confirm('确定要刷新该公告吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await announcementRefresh({ announcementId })
    getList()
  })
}

const handleAnnouncementOffline = async (announcementId) => {
  ElMessageBox.prompt('请填写下线原因', '提示', {
    inputPattern: /\S+/,
    inputErrorMessage: '请填写下线原因'
  }).then(async ({ value }) => {
    await announcementOffLine({ announcementId, reason: value })
    getList()
  })
}

const handleAnnouncementRepublish = async (announcementId) => {
  await announcementRepublish({ announcementId })
  getList()
}

const showAnnouncement = async (announcementId) => {
  await announcementIsShow({ announcementId })
  getList()
}

const hideAnnouncement = async (announcementId) => {
  await announcementIsHide({ announcementId })
  getList()
}

const handelLog = (id: string) => {
  logDialog.value.open(id)
}

const changeHideState = (changeState, type) => {
  ElMessageBox.confirm(`确定要${type === 1 ? '显示' : '隐藏'}文档吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const { content } = await changeState({ ids: announceIds.value })
    if (content) {
      ElMessageBox.alert(content, '提示', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false
      })
    }
    getList()
  })
}

const announceSelectBatch = async (val: number) => {
  batchValue.value = ''
  if (val === 7) {
    changeHideState(batchShow, 1)
  }

  if (val === 8) {
    changeHideState(batchHidden, 2)
  }

  if (val === 3) {
    ElMessageBox.confirm('确定要刷新该公告吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(async () => {
        await announcementBatchRefresh({ ids: announceIds.value })
        getList()
      })
      .catch(() => {})
  }
  if (val === 1) {
    await announcementBatchOnline({ ids: announceIds.value, actionType: val })
    getList()
  }
  if (val === 2) {
    ElMessageBox.prompt('请填写下线原因', '提示', {
      inputPattern: /\S+/,
      inputErrorMessage: '请填写下线原因'
    })
      .then(async ({ value }) => {
        if (!value) {
          ElMessage.warning('请填写下线原因')
          return
        }
        await announcementBatchOnline({
          ids: announceIds.value,
          actionType: val,
          reason: value
        })
        getList()
      })
      .catch(() => {})
  }
  if (val === 4) {
    const { attributeDocument: attribute } = <any>await getAnnounceSearchParams()
    attributeDocument.value = attribute
    announcementEdit.value.open(announceIds.value)
  }
  if (val === 5 || val === 6) {
    announcementBatchCopy.value.openBatchCopy(val, announceIds.value)
  }
}

const announceEdit = (id: string) => {
  router.push({ path: `/cms/announcementEdit/${id}` })
}

const toCompanyDetail = (id: string) => {
  router.push({
    path: '/company/details',
    query: { id }
  })
}

const downloadExcel = async () => {
  loading.value = true
  getAnnouncementList({ ...form.value, export: 1 })
    .then((res) => {
      window.location.href = res.excelUrl
      loading.value = false
    })
    .catch(() => {
      loading.value = false
    })
}

const getAttributeData = async (articleId: string) => {
  attributeData.value = await getAnnouncementAttributeData({ articleId })
}

const refreshSortTime = async (attributeId: string, articleId: string) => {
  await announcementRefreshSortTime({ attributeId, articleId })
}

const announceDel = async (announcementId) => {
  ElMessageBox.confirm('删除后公告及职位无法恢复，确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await announcementDelete({ announcementId })
    getList()
  })
}

const handleChangeSort = (row) => {
  const { aid: id, homeSort: sort } = row
  const callback = (newSort) => {
    row.homeSort = newSort
  }
  sortChangeDialog.value.open({ id, sort }, callback)
}

const setColumnMinWidth = (key: string) => {
  let minWidth = 90

  switch (key) {
    case 'amount':
      minWidth = 110
      break
    case 'sort':
      minWidth = 110
      break
    case 'jobNum':
      minWidth = 110
      break
    case 'onlineCount':
      minWidth = 110
      break
    case 'offlineCount':
      minWidth = 110
      break
    case 'platformNum':
      minWidth = 110
      break
    case 'emailNum':
      minWidth = 110
      break
    case 'click':
      minWidth = 110
      break
    case 'column':
      minWidth = 110
      break
    case 'firstData':
      minWidth = 140
      break
    case 'refreshTime':
      minWidth = 110
      break
    case 'realRefreshTime':
      minWidth = 110
      break
    case 'operation':
      minWidth = 210
      break
    case 'isMiniappTxt':
      minWidth = 110
      break

    default:
      minWidth = 90
      break
  }

  return minWidth
}

const handleShowMore = () => {
  showMore.value = !showMore.value
  getTableHeight()
}

const openBusiness = (announcementId: string) => {
  router.push({
    path: `/announcement/business`,
    query: { announcementId }
  })
}

const btnGroupEvent = async (key: string, row: any) => {
  const { announcementId } = row

  switch (key) {
    // 业务
    case 'business':
      await openBusiness(announcementId)
      break
    // 刷新
    case 'refresh':
      await refreshAnnounce(announcementId)
      break
    // 显示
    case 'show':
      await showAnnouncement(announcementId)
      break
    // 隐藏
    case 'hide':
      await hideAnnouncement(announcementId)
      break
    // 日志
    case 'log':
      await handelLog(announcementId)
      break
    // 编辑
    case 'edit':
      await announceEdit(announcementId)
      break
    // 下线
    case 'offline':
      await handleAnnouncementOffline(announcementId)
      break
    // 再发布
    case 'republish':
      await handleAnnouncementRepublish(announcementId)
      break
    // 删除
    case 'delete':
      await announceDel(announcementId)
      break

    default:
      break
  }
}
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.show {
  margin-left: 10px;
  white-space: nowrap;
  color: var(--color-primary);
}

.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
.column {
  .my-5 {
    margin-left: auto;
  }
}
.tip {
  font-size: 12px;
}
.danger {
  color: #d9041a;
  font-weight: bold;
}
.sort {
  display: flex;
  align-items: center;
  justify-content: center;
  .sort-edit {
    width: 20px;
    opacity: 0.6;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
