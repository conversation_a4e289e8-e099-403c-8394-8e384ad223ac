<template>
  <div class="box">
    <el-tabs type="card" v-model="active" @tab-change="handleTabChange">
      <el-tab-pane v-if="showTab.outer" label="站内投递" name="1">
        <el-form ref="receivedForm" :model="receivedFormData" class="mt-6" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="receivedFormData.resumeName"
              placeholder="人才姓名/编号/职位编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="receivedFormData.addTimeStart"
              v-model:end="receivedFormData.addTimeEnd"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="receivedFormData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getOnSideApplyData">搜索</el-button>
          </el-form-item>
          <div class="mb-10">
            共计投递{{ statistics.allApplyCount }}次； {{ statistics.allJobCount }}个职位
          </div>
        </el-form>
        <el-table :data="onSiteList" border size="large" v-loading="loading">
          <el-table-column
            prop="jobUuid"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeUuid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmentTitle"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          />
          <el-table-column
            prop="deliveryWay"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          />
          <el-table-column
            prop="status"
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
          />
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          v-if="pagination.total > 0"
          @change="receivedPaginationChange"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane v-if="showTab.outside" label="站外投递" name="2">
        <el-form ref="outSiteForm" :model="outSiteFormData" class="mt-6" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="outSiteFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="outSiteFormData.addTimeStart"
              v-model:end="outSiteFormData.addTimeEnd"
              placeholder="投递时间"
            />
          </el-form-item>
          <el-form-item prop="deliveryWay">
            <ResumeSource
              v-model="outSiteFormData.deliveryWay"
              placeholder="全部投递方式"
              :dataType="1"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getOutsiteData">搜索</el-button>
          </el-form-item>
          <div class="mb-10">
            共计投递{{ statistics.allApplyCount }}次； {{ statistics.allJobCount }}个职位
          </div>
        </el-form>
        <el-table :data="outSiteList" border size="large" v-loading="loading">
          <el-table-column
            prop="jobUuid"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeUuid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmentTitle"
            align="center"
            header-align="center"
            label="附件简历"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="deliveryWay"
            align="center"
            header-align="center"
            label="投递方式"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="投递进度"
            show-overflow-tooltip
            prop="status"
          >
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="outsitePaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane v-if="showTab.invite" label="面试邀约" name="3">
        <el-form ref="inviteForm" :model="inviteFormData" class="mt-6" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="inviteFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="interviewTimeStart">
            <DatePickerRange
              v-model:start="inviteFormData.interviewTimeStart"
              v-model:end="inviteFormData.interviewTimeEnd"
              placeholder="面试时间"
            />
          </el-form-item>
          <el-form-item prop="addTimeStart">
            <DatePickerRange
              v-model:start="inviteFormData.addTimeStart"
              v-model:end="inviteFormData.addTimeEnd"
              placeholder="面试创建时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getInviteData">搜索</el-button>
          </el-form-item>
          <div class="mb-10">
            共计面试{{ statistics.allInterviewCount }}次；
            {{ statistics.allInterviewJobCount }}个职位
          </div>
        </el-form>
        <el-table :data="inviteList" border size="large" v-loading="loading">
          <el-table-column
            prop="id"
            align="center"
            header-align="center"
            label="序号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobUuid"
            align="center"
            header-align="center"
            label="职位编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="jobName"
            align="center"
            header-align="center"
            label="职位名称"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeUuid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewTime"
            align="center"
            header-align="center"
            label="面试时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="addTime"
            align="center"
            header-align="center"
            label="投递时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="interviewAddTime"
            align="center"
            header-align="center"
            label="面试创建时间"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            align="center"
            header-align="center"
            label="面试详情"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              <el-popover
                placement="top"
                width="400px"
                trigger="click"
                @before-enter="handleGetInterviewDetail(row)"
              >
                <template #reference>
                  <el-link class="fs-13" :underline="false" type="primary">查看</el-link>
                </template>
                <div class="px-15">
                  <div class="fw-bold my-15 flex">
                    面试信息
                    <span class="invite-amount">{{ interviewDetail.interviewAmount }}面</span>
                  </div>
                  <div class="mb-10">面试职位：{{ interviewDetail.jobName }}</div>
                  <div class="mb-10">面试时间：{{ interviewDetail.interviewTime }}</div>
                  <div class="mb-10">面试地址：{{ interviewDetail.address }}</div>
                  <div class="mb-10">联 系 人：{{ interviewDetail.contact }}</div>
                  <div class="mb-10">联系电话：{{ interviewDetail.telephone }}</div>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="invitePaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
      <el-tab-pane v-if="showTab.downloadResume" label="下载的简历" name="4">
        <el-form ref="downloadForm" :model="downloadFormData" class="mt-6" :inline="true">
          <el-form-item prop="resumeName">
            <el-input
              v-model="downloadFormData.resumeName"
              placeholder="人才姓名/编号"
              clearable
            ></el-input>
          </el-form-item>
          <el-form-item prop="downloadTimeStart">
            <DatePickerRange
              v-model:start="downloadFormData.downloadTimeStart"
              v-model:end="downloadFormData.downloadTimeEnd"
              placeholder="下载时间"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="getDownloadData">搜索</el-button>
          </el-form-item>
          <div class="mb-10">
            共计下载{{ statistics.allResumeDownloadLogCount }}次；
            {{ statistics.allResumeCount }}个人才
          </div>
        </el-form>
        <el-table :data="downloadList" border size="large" v-loading="loading">
          <el-table-column
            prop="id"
            align="center"
            header-align="center"
            label="序号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeUuid"
            align="center"
            header-align="center"
            label="人才编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeName"
            align="center"
            header-align="center"
            label="人才姓名"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeTypeTitle"
            align="center"
            header-align="center"
            label="简历类型"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="resumeAttachmentTitle"
            align="center"
            header-align="center"
            label="简历编号"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="downloadTime"
            align="center"
            header-align="center"
            label="下载时间"
            show-overflow-tooltip
          ></el-table-column>
          <template #empty>
            <el-empty description="暂无数据"></el-empty>
          </template>
        </el-table>
        <Pagination
          @change="downloadPaginationChange"
          v-if="pagination.total > 0"
          class="mt-15"
          :total="pagination.total"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { toRefs, reactive, onMounted, ref, computed } from 'vue'
import { useRoute } from 'vue-router'

import DatePickerRange from '/@/components/base/datePickerRange.vue'
import Pagination from '/@/components/base/paging.vue'
import ResumeSource from '/@select/resumeSource.vue'

import {
  getApplyInit,
  getOnSideApplyList,
  getOnSideApplyStat,
  getInterviewList,
  getInterviewStat,
  getInterviewDetail,
  getOutsideApplyList,
  getOutsideApplyStat,
  getResumeDownloadLog,
  getResumeDownloadLogStat
} from '/@/api/cooperaAnnouncement'

// 定义名字
defineOptions({
  name: 'announcementBusiness'
})

const route = useRoute()
const state = reactive({
  loading: false,
  fromType: 'announcementId', // 来源 announcementId: 公告 jobId: 职位
  active: '1',
  id: '',
  receivedFormData: {
    id: computed(() => state.id),
    resumeName: '',
    addTimeStart: '',
    addTimeEnd: '',
    deliveryWay: '',
    page: 1,
    limit: 20
  },
  outSiteFormData: {
    id: computed(() => state.id),
    resumeName: '',
    addTimeStart: '',
    addTimeEnd: '',
    deliveryWay: '',
    page: 1,
    limit: 20
  },
  inviteFormData: {
    id: computed(() => state.id),
    resumeName: '',
    interviewTimeStart: '',
    interviewTimeEnd: '',
    addTimeStart: '',
    addTimeEnd: '',
    page: 1,
    limit: 20
  },
  downloadFormData: {
    id: computed(() => state.id),
    resumeName: '',
    downloadTimeStart: '',
    downloadTimeEnd: '',
    page: 1,
    limit: 20
  },
  pagination: {
    total: 0
  },
  outSiteList: [],
  onSiteList: [],
  inviteList: [],
  downloadList: [],
  statistics: {
    allApplyCount: 0,
    allJobCount: 0,
    allInterviewCount: 0,
    allInterviewJobCount: 0,
    allResumeDownloadLogCount: 0,
    allResumeCount: 0
  },
  interviewDetail: {
    interviewAmount: 0,
    jobName: '',
    interviewTime: '',
    address: '',
    contact: '',
    telephone: ''
  },
  showTab: {
    outer: 0, // 站内投递
    outside: 0, // 站外投递
    invite: 0, // 面试邀约
    downloadResume: 0 // 下载简历
  },
  deliveryTypeParams: [] // 投递方式参数
})

const handleGetInterviewDetail = (row: any) => {
  getInterviewDetail({ id: row.id }).then((resp: any) => {
    state.interviewDetail = resp
  })
}

const getApplyInitData = () => {
  getApplyInit({ [state.fromType]: state.id }).then((resp: any) => {
    state.showTab = resp.showTab
    // tabNameMap 用于映射 showTab 的 key 到 tab 的 name
    const tabNameMap = {
      outer: '1',
      outside: '2',
      invite: '3',
      downloadResume: '4'
    }
    // 按照 tab 的顺序，找到第一个为 true 的 tab
    for (const key of ['outer', 'outside', 'invite', 'downloadResume']) {
      if (resp.showTab[key]) {
        state.active = tabNameMap[key]
        handleTabChange()
        break
      }
    }
  })
}

const getOnSideApplyStatData = () => {
  getOnSideApplyStat(Object.assign(state.receivedFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.statistics = {
        ...state.statistics,
        ...resp
      }
    }
  )
}

const getOnSideApplyData = () => {
  state.loading = true
  getOnSideApplyList(Object.assign(state.receivedFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.loading = false
      state.onSiteList = resp.list
      state.pagination.total = Number(resp.page.count)
    }
  )
  getOnSideApplyStatData()
}

const getInviteStatData = () => {
  getInterviewStat(Object.assign(state.inviteFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.statistics = {
        ...state.statistics,
        ...resp
      }
    }
  )
}

const getInviteData = () => {
  state.loading = true
  getInterviewList(Object.assign(state.inviteFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.loading = false
      state.inviteList = resp.list
      state.pagination.total = Number(resp.page.count)
    }
  )
  getInviteStatData()
}

const getDownloadStatData = () => {
  getResumeDownloadLogStat(
    Object.assign(state.downloadFormData, { [state.fromType]: state.id })
  ).then((resp: any) => {
    state.statistics = {
      ...state.statistics,
      ...resp
    }
  })
}
const getDownloadData = () => {
  state.loading = true
  getResumeDownloadLog(Object.assign(state.downloadFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.loading = false
      state.downloadList = resp.list
      state.pagination.total = Number(resp.page.count)
    }
  )
  getDownloadStatData()
}

const getOutsiteStatData = () => {
  getOutsideApplyStat(Object.assign(state.outSiteFormData, { [state.fromType]: state.id })).then(
    (resp: any) => {
      state.statistics = {
        ...state.statistics,
        ...resp
      }
    }
  )
}

const getOutsiteData = () => {
  state.loading = true
  getOutsideApplyList(Object.assign(state.outSiteFormData, { [state.fromType]: state.id })).then(
    (res: any) => {
      state.loading = false
      state.outSiteList = res.list
      state.pagination.total = Number(res.page.count)
    }
  )
  getOutsiteStatData()
}

const handleTabChange = () => {
  switch (state.active) {
    case '1':
      getOnSideApplyData()
      break
    case '2':
      getOutsiteData()
      break
    case '3':
      getInviteData()
      break
    case '4':
      getDownloadData()
      break
    default:
      break
  }
}

onMounted(() => {
  const {
    query: { announcementId, jobId, active }
  } = route

  if (announcementId) {
    state.id = String(announcementId)
    state.fromType = 'announcementId'
  }
  if (jobId) {
    state.id = String(jobId)
    state.fromType = 'jobId'
  }

  getApplyInitData()
})

const receivedForm = ref()
const inviteForm = ref()
const downloadForm = ref()
const outSiteForm = ref()

const receivedPaginationChange = (data: any) => {
  state.receivedFormData.limit = data.limit
  state.receivedFormData.page = data.page
  getOnSideApplyData()
}
const invitePaginationChange = (data: any) => {
  state.inviteFormData.limit = data.limit
  state.inviteFormData.page = data.page
  getInviteData()
}
const downloadPaginationChange = (data: any) => {
  state.downloadFormData.limit = data.limit
  state.downloadFormData.page = data.page
  getDownloadData()
}

const outsitePaginationChange = (data: any) => {
  state.outSiteFormData.limit = data.limit
  state.outSiteFormData.page = data.page
  getOutsiteData()
}

const {
  loading,
  active,
  receivedFormData,
  outSiteFormData,
  inviteFormData,
  downloadFormData,
  pagination,
  outSiteList,
  onSiteList,
  inviteList,
  downloadList,
  statistics,
  showTab,
  interviewDetail
} = toRefs(state)
</script>

<style scoped lang="scss">
.el-form--inline .el-form-item {
  margin-right: 10px;
}

.invite-amount {
  font-size: 10px;
  color: #02a7f0;
  background-color: #f2f2f2;
  align-self: center;
  padding: 1px 8px;
  border-radius: 2px;
  margin-left: 10px;
}
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  a {
    text-decoration: none;
  }
}
</style>
