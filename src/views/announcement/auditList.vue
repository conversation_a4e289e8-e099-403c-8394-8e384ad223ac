<template>
  <div class="box">
    <div id="top-container" class="pb-20">
      <div class="filter-container-template">
        <el-form class="filter-grid-6" :model="formData" ref="announcementForm">
          <el-form-item label="公告检索" prop="announcementTitleNum">
            <el-input
              v-model="formData.announcementTitleNum"
              placeholder="请输入公告标题或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>
          <el-form-item label="职位检索" prop="jobName">
            <el-input
              v-model="formData.jobName"
              placeholder="请输入职位标题或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位检索" prop="companyName">
            <el-input
              v-model="formData.companyName"
              placeholder="请输入单位名称或编号"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属主栏目" prop="homeColumnId">
            <Colunm v-model="formData.homeColumnId" :columnList="columnList" />
          </el-form-item>
          <el-form-item label="申请人" prop="username">
            <el-input
              v-model="formData.username"
              placeholder="请输入申请人信息"
              clearable
              @keyup.enter="search"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="filter-tools">
          <div class="filter-btn-group">
            <slot name="right">
              <el-button type="primary" @click="search">搜索</el-button>
              <el-button @click="resetForm">重置</el-button>
            </slot>
          </div>
        </div>
      </div>

      <div class="jc-between amount">
        <div>
          共计:
          <span class="danger">{{ total }}</span>
          则公告；
        </div>
        <div v-if="selectedRows.length">
          <el-button type="primary" @click="batchAudit"
            >批量审核({{ selectedRows.length }})</el-button
          >
        </div>
      </div>
    </div>

    <el-table
      :data="auditList"
      border
      :max-height="maxTableHeight"
      @sort-change="handleSortChange"
      @selection-change="handleSelectionChange"
      v-loading="loading"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column align="center" prop="announcementUuId" label="公告id" :min-width="90">
      </el-table-column>
      <el-table-column align="center" prop="title" label="公告标题" :min-width="130">
        <template #default="{ row }">
          <router-link
            class="bg-primary td-none"
            :to="`/cms/announcementDetail/${row.announcementId}/${row.status}`"
            >{{ row.title }}</router-link
          >
          <p class="danger">
            {{ row.tip }}
          </p>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="fullName" label="所属单位" :min-width="90">
        <template #default="{ row }">
          <el-link type="primary" :underline="false" @click="toCompanyDetail(row.companyId)">{{
            row.companyName
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="sortOnlineJobAmount"
        label="在线职位"
        sortable="custom"
        :min-width="90"
      >
        <template #default="{ row }">
          {{ row.onlineJobAmount }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="sortAuditWaitJob"
        label="待审核职位"
        sortable="custom"
        :min-width="100"
      >
        <template #default="{ row }">
          {{ row.auditWaitJob }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="statusTxt" label="招聘状态" :min-width="90">
      </el-table-column>
      <el-table-column align="center" prop="applyAdminName" label="申请人" :min-width="90">
      </el-table-column>
      <el-table-column align="center" prop="creator" label="创建人" :min-width="90" />
      <el-table-column
        align="center"
        prop="sortAddTime"
        label="创建时间"
        sortable="custom"
        :min-width="90"
      >
        <template #default="{ row }">
          {{ row.addTime }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="sortApplyAuditTime"
        label="申请审核时间"
        sortable="custom"
        :min-width="110"
      >
        <template #default="{ row }">
          {{ row.applyAuditTime }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        prop="sortRefreshTime"
        label="发布时间"
        sortable="custom"
        :min-width="90"
      >
        <template #default="{ row }">
          {{ row.refreshTime }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" :min-width="90">
        <template #default="{ row }">
          <el-button size="small" type="success">
            <router-link
              class="td-none color-white"
              :to="`/cms/announcementAuditDetail/${row.announcementId}`"
              >审核</router-link
            >
          </el-button></template
        >
      </el-table-column>
    </el-table>

    <div
      id="bottom-container"
      v-show="total > 0"
      class="pt-15 jc-end ai-center"
      style="flex-shrink: 0"
    >
      <Paging
        :total="total"
        :default-page-size="formData.pageSize"
        @change="handlePaginationChange"
      />
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'announcementAuditList'
}
</script>

<script setup lang="ts">
import { ref, reactive, onActivated, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  getAnnounceSearchParams,
  getAnnouncementAuditList,
  getCmsAnnouncementAuditList
} from '/@/api/announcement'
import Colunm from '/@/components/base/colunm.vue'
import Paging from '/@/components/base/paging.vue'

interface FormData {
  announcementTitleNum: string
  jobName: string
  companyName: string
  homeColumnId: string
  username: string
  page: number
  pageSize: number
  [key: string]: any
}

interface AttributeItem {
  k: string
  v: string
}

const router = useRouter()
const route = useRoute()
const announcementForm = ref()
const loading = ref(true)
const selectedRows = ref<any[]>([])
const maxTableHeight = ref(450)
// 1：合作单位，2：非合作单位
const announcementType = ref(1)

const getAnnouncementType = () => {
  announcementType.value = route.name === 'announcementAuditList' ? 1 : 2
}
getAnnouncementType()

const formData = reactive<FormData>({
  announcementTitleNum: '',
  jobName: '',
  companyName: '',
  homeColumnId: '',
  username: '',
  attribute: '',
  firstReleaseTimeStart: '',
  firstReleaseTimeEnd: '',
  applyAuditTimeStart: '',
  applyAuditTimeEnd: '',
  page: 1,
  pageSize: 20
})

const auditList = ref([])
const columnList = ref([])
const attributeDocument = ref<AttributeItem[]>([])
const total = ref(0)

const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

const search = async (sort = {}) => {
  loading.value = true

  const fetchApi =
    announcementType.value === 1 ? getAnnouncementAuditList : getCmsAnnouncementAuditList
  const { list, page } = await fetchApi({ ...formData, ...sort })
  auditList.value = list
  total.value = page.count
  formData.pageSize = page.limit
  loading.value = false

  getTableHeight()
}

const getData = async () => {
  const { columnList: cols, attributeDocument: attrs } = await getAnnounceSearchParams()
  columnList.value = cols
  attributeDocument.value = attrs
  search()
}

const toCompanyDetail = (id: string) => {
  router.push({
    path: '/company/details',
    query: { id }
  })
}

const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sort = order === 'ascending' ? 2 : 1
  search({ [prop]: sort })
}

const resetForm = () => {
  announcementForm.value?.resetFields()
  getData()
}

const handlePaginationChange = (data: { page: number; limit: number }) => {
  formData.page = data.page
  formData.pageSize = data.limit
  search()
}

const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

const batchAudit = () => {
  const aids = selectedRows.value.map((row) => row.announcementId)
  const aid = aids[0]
  const aids2 = aids.slice(1)
  const url = `/cms/announcementAuditDetail/${aid}`

  if (aids2.length) {
    router.push({
      path: url,
      query: {
        nextIds: aids2.join(',')
      }
    })
  } else {
    router.push(url)
  }
}

onActivated(() => {
  search()
})

getData()
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;

  .danger {
    color: #d9041a;
    font-weight: bold;
  }
}
.amount {
  margin: 20px 0;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
</style>
