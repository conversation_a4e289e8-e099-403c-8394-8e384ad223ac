<template>
  <div class="main">
    <div class="detail-container">
      <div class="detail-header">
        <h2>限制详情</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="handleEdit">编辑</el-button>
        </div>
      </div>
      
      <div class="detail-content" v-loading="loading">
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
                {{ detail.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="限制ID">
              {{ detail.id }}
            </el-descriptions-item>
            <el-descriptions-item label="限制名称">
              {{ detail.name }}
            </el-descriptions-item>
            <el-descriptions-item label="单位ID">
              {{ detail.company_id }}
            </el-descriptions-item>
            <el-descriptions-item label="限制类型">
              {{ getLimitTypeText(detail.limit_type) }}
            </el-descriptions-item>
            
            <!-- 次数限制相关字段 -->
            <template v-if="detail.limit_type === 'count'">
              <el-descriptions-item label="时间限制(天)">
                {{ detail.time_limit }}
              </el-descriptions-item>
              <el-descriptions-item label="次数限制">
                {{ detail.count_limit }}
              </el-descriptions-item>
              <el-descriptions-item label="限制描述" :span="2">
                {{ detail.time_limit }}天内最多投递{{ detail.count_limit }}次
              </el-descriptions-item>
            </template>
            
            <!-- 条件限制相关字段 -->
            <template v-if="detail.limit_type === 'condition'">
              <el-descriptions-item label="条件字段">
                {{ getConditionFieldText(detail.condition_field) }}
              </el-descriptions-item>
              <el-descriptions-item label="条件值">
                {{ detail.condition_value }}
              </el-descriptions-item>
              <el-descriptions-item label="限制描述" :span="2">
                {{ getConditionFieldText(detail.condition_field) }}: {{ detail.condition_value }}
              </el-descriptions-item>
            </template>
            
            <el-descriptions-item label="错误提示信息" :span="2">
              <div class="error-message">
                {{ detail.error_message }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ detail.remark || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ detail.created_by }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ detail.add_time }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <!-- 限制规则说明 -->
        <el-card class="detail-card">
          <template #header>
            <span>限制规则说明</span>
          </template>
          
          <div class="rule-description">
            <div v-if="detail.limit_type === 'count'" class="rule-item">
              <h4>次数限制规则：</h4>
              <ul>
                <li>在{{ detail.time_limit }}天内，同一用户最多可以向该单位投递{{ detail.count_limit }}次简历</li>
                <li>超出限制时，系统将显示错误提示：{{ detail.error_message }}</li>
                <li>时间窗口为滚动窗口，从用户第一次投递开始计算</li>
              </ul>
            </div>
            
            <div v-if="detail.limit_type === 'condition'" class="rule-item">
              <h4>条件限制规则：</h4>
              <ul>
                <li>当用户的{{ getConditionFieldText(detail.condition_field) }}为"{{ detail.condition_value }}"时，禁止投递</li>
                <li>不满足条件时，系统将显示错误提示：{{ detail.error_message }}</li>
                <li>条件检查在每次投递时进行</li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getLimitDetail } from '/@/api/specialNeed'

const router = useRouter()
const route = useRoute()

// 详情数据
const detail = ref<any>({})
const loading = ref(false)

// 获取详情
const getDetail = async () => {
  loading.value = true
  try {
    const id = route.params.id
    const res = await getLimitDetail({ id })
    detail.value = res
  } catch (error) {
    console.error('获取限制详情失败:', error)
    ElMessage.error('获取限制详情失败')
  } finally {
    loading.value = false
  }
}

// 获取限制类型文本
const getLimitTypeText = (type: string) => {
  const typeMap = {
    count: '次数限制',
    condition: '条件限制'
  }
  return typeMap[type] || type
}

// 获取条件字段文本
const getConditionFieldText = (field: string) => {
  const fieldMap = {
    is_abroad: '海外经历',
    education_level: '学历水平',
    work_experience: '工作经验'
  }
  return fieldMap[field] || field
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 编辑
const handleEdit = () => {
  router.push(`/special-need/limit/edit/${detail.value.id}`)
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.detail-content {
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.error-message {
  color: #f56c6c;
  font-weight: 500;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
}

.rule-description {
  .rule-item {
    h4 {
      margin: 0 0 10px 0;
      color: #303133;
      font-size: 16px;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        line-height: 1.6;
        color: #606266;
      }
    }
  }
}
</style>
