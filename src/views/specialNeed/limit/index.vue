<template>
  <div class="main">
    <div class="formbigbox">
      <div class="formbox">
        <el-form
          ref="form"
          :model="formData"
          label-width="100px"
          class="demo-form-inline"
          :inline="true"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="限制名称" prop="name">
                <el-input
                  v-model="formData['SpecialNeedApplyLimit[name]']"
                  placeholder="请输入限制名称"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="单位ID" prop="company_id">
                <el-input
                  v-model="formData['SpecialNeedApplyLimit[company_id]']"
                  placeholder="请输入单位ID"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="限制类型" prop="limit_type">
                <el-select
                  v-model="formData['SpecialNeedApplyLimit[limit_type]']"
                  placeholder="全部"
                  clearable
                >
                  <el-option label="次数限制" value="count"></el-option>
                  <el-option label="条件限制" value="condition"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="formData['SpecialNeedApplyLimit[status]']"
                  placeholder="全部"
                  clearable
                >
                  <el-option label="启用" :value="1"></el-option>
                  <el-option label="禁用" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button type="primary" @click="getData">搜索</el-button>
            <el-button type="default" @click="resetForm">重置</el-button>
            <el-button type="success" @click="handleAdd">新增限制</el-button>
          </el-form-item>
        </el-form>

        <div class="unitCount">
          <div>
            共计：<span class="danger">{{ pagination.total }}</span> 条限制
          </div>
        </div>

        <div class="showTable">
          <el-table v-loading="loading" :data="tableData" border style="width: 100%" align="center">
            <el-table-column prop="id" label="ID" width="80" align="center" />
            <el-table-column prop="name" label="限制名称" min-width="150" align="center" />
            <el-table-column prop="company_info" label="单位信息" min-width="120" align="center" />
            <el-table-column prop="limit_type_text" label="限制类型" width="100" align="center" />
            <el-table-column
              prop="limit_description"
              label="限制描述"
              min-width="200"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column label="限制详情" min-width="150" align="center">
              <template #default="{ row }">
                <div v-if="row.limit_type === 'count'">
                  {{ row.time_limit }}天内最多{{ row.count_limit }}次
                </div>
                <div v-else-if="row.limit_type === 'condition'">
                  {{ row.condition_field }}: {{ row.condition_value }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="error_message"
              label="错误提示"
              min-width="200"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column prop="status_text" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status_text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="created_by_name" label="创建人" width="100" align="center" />
            <el-table-column prop="add_time" label="创建时间" width="150" align="center" />
            <el-table-column label="操作" fixed="right" width="180" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                <el-button type="success" size="small" @click="handleEdit(row.id)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <Paging :total="pagination.total" @change="changePage"></Paging>
    </div>

    <!-- 限制弹窗 -->
    <LimitDialog v-model:visible="dialogVisible" :edit-id="editId" @success="handleDialogSuccess" />

    <!-- 详情弹窗 -->
    <LimitDetailDialog v-model:visible="detailVisible" :detail-data="detailData" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getLimitList, deleteLimit } from '/@/api/specialNeed'
import Paging from '/@/components/base/paging.vue'
import LimitDialog from './components/LimitDialog.vue'
import LimitDetailDialog from './components/LimitDetailDialog.vue'

// 表单数据
const formData = reactive({
  'SpecialNeedApplyLimit[name]': '',
  'SpecialNeedApplyLimit[company_id]': '',
  'SpecialNeedApplyLimit[limit_type]': '',
  'SpecialNeedApplyLimit[status]': '',
  page: 1,
  'per-page': 20
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
  total: 0
})

// 弹窗相关
const dialogVisible = ref(false)
const editId = ref<number | string>()
const detailVisible = ref(false)
const detailData = ref<any>({})

// 获取数据
const getData = async () => {
  loading.value = true
  try {
    const res = await getLimitList(formData)
    tableData.value = res.items || []
    pagination.total = res.pagination?.totalCount || 0
  } catch (error) {
    console.error('获取限制列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach((key) => {
    if (key !== 'page' && key !== 'per-page') {
      formData[key] = ''
    }
  })
  formData.page = 1
  getData()
}

// 分页
const changePage = (pageInfo: any) => {
  formData.page = pageInfo.page
  formData['per-page'] = pageInfo.limit
  getData()
}

// 新增
const handleAdd = () => {
  editId.value = undefined
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  detailData.value = row
  detailVisible.value = true
}

// 编辑
const handleEdit = (id: number) => {
  editId.value = id
  dialogVisible.value = true
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  getData()
}

// 删除
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条限制吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteLimit({ id })
    ElMessage.success('删除成功')
    getData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

onMounted(() => {
  getData()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.formbigbox {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.formbox {
  .demo-form-inline {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
}

.unitCount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0 15px 0;

  .danger {
    color: #f56c6c;
    font-weight: bold;
  }
}

.showTable {
  .el-table {
    border-radius: 4px;
    overflow: hidden;
  }
}

#bottom-container {
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pt-15 {
  padding-top: 15px;
}

.jc-between {
  display: flex;
  justify-content: space-between;
}

.ai-center {
  display: flex;
  align-items: center;
}
</style>
