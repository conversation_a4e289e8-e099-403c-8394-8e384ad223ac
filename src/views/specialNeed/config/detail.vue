<template>
  <div class="main">
    <div class="detail-container">
      <div class="detail-header">
        <h2>配置详情</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="handleEdit">编辑</el-button>
        </div>
      </div>
      
      <div class="detail-content" v-loading="loading">
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <span>基本信息</span>
              <el-tag :type="detail.status === 1 ? 'success' : 'danger'">
                {{ detail.status === 1 ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </template>
          
          <el-descriptions :column="2" border>
            <el-descriptions-item label="配置ID">
              {{ detail.id }}
            </el-descriptions-item>
            <el-descriptions-item label="配置名称">
              {{ detail.name }}
            </el-descriptions-item>
            <el-descriptions-item label="配置类型">
              {{ getTypeText(detail.type) }}
            </el-descriptions-item>
            <el-descriptions-item label="目标ID">
              {{ detail.target_id }}
            </el-descriptions-item>
            <el-descriptions-item label="字段名称">
              {{ detail.field_name }}
            </el-descriptions-item>
            <el-descriptions-item label="适用平台">
              {{ getPlatformText(detail.platform) }}
            </el-descriptions-item>
            <el-descriptions-item label="生效开始时间" :span="2">
              {{ detail.start_time || '无限制' }}
            </el-descriptions-item>
            <el-descriptions-item label="生效结束时间" :span="2">
              {{ detail.end_time || '无限制' }}
            </el-descriptions-item>
            <el-descriptions-item label="字段值" :span="2">
              <div class="field-value">
                {{ detail.field_value }}
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="备注" :span="2">
              {{ detail.remark || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="创建人">
              {{ detail.created_by }}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间">
              {{ detail.add_time }}
            </el-descriptions-item>
            <el-descriptions-item label="更新时间" :span="2">
              {{ detail.update_time }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
        
        <el-card class="detail-card" v-if="detail.type">
          <template #header>
            <span>配置测试</span>
          </template>
          
          <el-form :model="testForm" label-width="120px" :inline="true">
            <el-form-item label="配置类型">
              <el-input v-model="testForm.type" :value="detail.type" disabled />
            </el-form-item>
            <el-form-item label="目标ID">
              <el-input v-model="testForm.target_id" :value="detail.target_id" disabled />
            </el-form-item>
            <el-form-item label="平台">
              <el-select v-model="testForm.platform" placeholder="请选择平台">
                <el-option label="PC端" value="PC" />
                <el-option label="H5端" value="H5" />
                <el-option label="小程序" value="MINI" />
                <el-option label="全平台" value="ALL" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleTest" :loading="testLoading">
                测试配置
              </el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="testResult" class="test-result">
            <h4>测试结果：</h4>
            <el-alert
              :title="testResult.message"
              :type="testResult.success ? 'success' : 'error'"
              show-icon
              :closable="false"
            />
            <div v-if="testResult.data && testResult.data.length" class="test-data">
              <h5>匹配的配置：</h5>
              <el-table :data="testResult.data" border size="small">
                <el-table-column prop="id" label="ID" width="80" />
                <el-table-column prop="name" label="配置名称" />
                <el-table-column prop="field_name" label="字段名称" />
                <el-table-column prop="field_value" label="字段值" show-overflow-tooltip />
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { getConfigDetail, testConfig } from '/@/api/specialNeed'

const router = useRouter()
const route = useRoute()

// 详情数据
const detail = ref<any>({})
const loading = ref(false)

// 测试表单
const testForm = reactive({
  type: '',
  target_id: '',
  platform: 'PC'
})

const testResult = ref<any>(null)
const testLoading = ref(false)

// 获取详情
const getDetail = async () => {
  loading.value = true
  try {
    const id = route.params.id
    const res = await getConfigDetail({ id })
    detail.value = res
    
    // 设置测试表单默认值
    testForm.type = res.type
    testForm.target_id = res.target_id
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('获取配置详情失败')
  } finally {
    loading.value = false
  }
}

// 获取类型文本
const getTypeText = (type: string) => {
  const typeMap = {
    job: '职位信息',
    announcement: '公告信息',
    company: '单位信息'
  }
  return typeMap[type] || type
}

// 获取平台文本
const getPlatformText = (platform: string) => {
  const platformMap = {
    ALL: '全平台',
    PC: 'PC端',
    H5: 'H5端',
    MINI: '小程序'
  }
  return platformMap[platform] || platform
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 编辑
const handleEdit = () => {
  router.push(`/special-need/config/edit/${detail.value.id}`)
}

// 测试配置
const handleTest = async () => {
  testLoading.value = true
  try {
    const res = await testConfig(testForm)
    testResult.value = res
  } catch (error) {
    console.error('测试配置失败:', error)
    testResult.value = {
      success: false,
      message: '测试失败'
    }
  } finally {
    testLoading.value = false
  }
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.detail-container {
  max-width: 1200px;
  margin: 0 auto;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.detail-content {
  .detail-card {
    margin-bottom: 20px;
    
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
}

.field-value {
  max-width: 500px;
  word-break: break-all;
  white-space: pre-wrap;
}

.test-result {
  margin-top: 20px;
  
  h4, h5 {
    margin: 10px 0;
    color: #303133;
  }
  
  .test-data {
    margin-top: 15px;
  }
}
</style>
