<template>
  <div class="main">
    <div class="edit-container">
      <div class="edit-header">
        <h2>{{ isEdit ? '编辑配置' : '新增配置' }}</h2>
        <div class="header-actions">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="handleSave" :loading="saveLoading">
            保存
          </el-button>
        </div>
      </div>
      
      <div class="edit-content">
        <el-card>
          <el-form
            ref="formRef"
            :model="formData"
            :rules="formRules"
            label-width="120px"
            v-loading="loading"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="配置名称" prop="name">
                  <el-input
                    v-model="formData.name"
                    placeholder="请输入配置名称"
                    maxlength="100"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="配置类型" prop="type">
                  <el-select
                    v-model="formData.type"
                    placeholder="请选择配置类型"
                    @change="handleTypeChange"
                    style="width: 100%"
                  >
                    <el-option label="职位信息" value="job" />
                    <el-option label="公告信息" value="announcement" />
                    <el-option label="单位信息" value="company" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="目标ID" prop="target_id">
                  <el-input
                    v-model="formData.target_id"
                    placeholder="请输入目标ID"
                    type="number"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="字段名称" prop="field_name">
                  <el-select
                    v-model="formData.field_name"
                    placeholder="请选择字段名称"
                    style="width: 100%"
                    filterable
                    :loading="fieldOptionsLoading"
                  >
                    <el-option
                      v-for="(label, value) in fieldOptions"
                      :key="value"
                      :label="label"
                      :value="value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="字段值" prop="field_value">
              <el-input
                v-model="formData.field_value"
                type="textarea"
                :rows="4"
                placeholder="请输入字段值"
                maxlength="5000"
                show-word-limit
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="适用平台" prop="platform">
                  <el-select v-model="formData.platform" placeholder="请选择适用平台" style="width: 100%">
                    <el-option label="全平台" value="ALL" />
                    <el-option label="PC端" value="PC" />
                    <el-option label="H5端" value="H5" />
                    <el-option label="小程序" value="MINI" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-radio-group v-model="formData.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="0">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="生效开始时间">
                  <el-date-picker
                    v-model="formData.start_time"
                    type="datetime"
                    placeholder="选择开始时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="生效结束时间">
                  <el-date-picker
                    v-model="formData.end_time"
                    type="datetime"
                    placeholder="选择结束时间"
                    format="YYYY-MM-DD HH:mm:ss"
                    value-format="YYYY-MM-DD HH:mm:ss"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item label="备注">
              <el-input
                v-model="formData.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入备注信息"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  getConfigDetail, 
  createConfig, 
  updateConfig,
  getFieldOptions
} from '/@/api/specialNeed'

const router = useRouter()
const route = useRoute()

// 是否编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单引用
const formRef = ref()

// 表单数据
const formData = reactive({
  name: '',
  type: '',
  target_id: '',
  field_name: '',
  field_value: '',
  platform: 'ALL',
  status: 1,
  start_time: '',
  end_time: '',
  remark: ''
})

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入配置名称', trigger: 'blur' },
    { max: 100, message: '配置名称不能超过100个字符', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请选择配置类型', trigger: 'change' }
  ],
  target_id: [
    { required: true, message: '请输入目标ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '目标ID必须是数字', trigger: 'blur' }
  ],
  field_name: [
    { required: true, message: '请选择字段名称', trigger: 'change' }
  ],
  field_value: [
    { required: true, message: '请输入字段值', trigger: 'blur' },
    { max: 5000, message: '字段值不能超过5000个字符', trigger: 'blur' }
  ],
  platform: [
    { required: true, message: '请选择适用平台', trigger: 'change' }
  ]
}

// 状态
const loading = ref(false)
const saveLoading = ref(false)
const fieldOptionsLoading = ref(false)
const fieldOptions = ref({})

// 获取字段选项
const loadFieldOptions = async (type: string) => {
  if (!type) return
  
  fieldOptionsLoading.value = true
  try {
    const res = await getFieldOptions({ type })
    fieldOptions.value = res
  } catch (error) {
    console.error('获取字段选项失败:', error)
  } finally {
    fieldOptionsLoading.value = false
  }
}

// 类型变化
const handleTypeChange = (type: string) => {
  formData.field_name = ''
  loadFieldOptions(type)
}

// 获取详情（编辑模式）
const getDetail = async () => {
  if (!isEdit.value) return
  
  loading.value = true
  try {
    const id = route.params.id
    const res = await getConfigDetail({ id })
    
    // 填充表单数据
    Object.keys(formData).forEach(key => {
      if (res[key] !== undefined) {
        formData[key] = res[key]
      }
    })
    
    // 加载字段选项
    if (formData.type) {
      await loadFieldOptions(formData.type)
    }
  } catch (error) {
    console.error('获取配置详情失败:', error)
    ElMessage.error('获取配置详情失败')
  } finally {
    loading.value = false
  }
}

// 返回
const goBack = () => {
  router.go(-1)
}

// 保存
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    saveLoading.value = true
    
    const data = {
      SpecialNeedConfig: { ...formData }
    }
    
    if (isEdit.value) {
      const id = route.params.id
      await updateConfig({ ...data, id })
      ElMessage.success('更新成功')
    } else {
      await createConfig(data)
      ElMessage.success('创建成功')
    }
    
    router.push('/special-need/config')
  } catch (error) {
    if (error !== false) { // 表单验证失败时error为false
      console.error('保存失败:', error)
      ElMessage.error('保存失败')
    }
  } finally {
    saveLoading.value = false
  }
}

onMounted(() => {
  getDetail()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.edit-container {
  max-width: 1000px;
  margin: 0 auto;
}

.edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  h2 {
    margin: 0;
    color: #303133;
  }
}

.edit-content {
  .el-card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
}
</style>
