<template>
  <div class="main">
    <div class="formbigbox">
      <div class="formbox">
        <el-form ref="form" :model="formData" label-width="100px" class="demo-form-inline" :inline="true">
          <el-row>
            <el-col :span="6">
              <el-form-item label="配置名称" prop="name">
                <el-input
                  v-model="formData['SpecialNeedConfig[name]']"
                  placeholder="请输入配置名称"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="配置类型" prop="type">
                <el-select v-model="formData['SpecialNeedConfig[type]']" placeholder="全部" clearable>
                  <el-option label="职位信息" value="job"></el-option>
                  <el-option label="公告信息" value="announcement"></el-option>
                  <el-option label="单位信息" value="company"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="目标ID" prop="target_id">
                <el-input
                  v-model="formData['SpecialNeedConfig[target_id]']"
                  placeholder="请输入目标ID"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-select v-model="formData['SpecialNeedConfig[status]']" placeholder="全部" clearable>
                  <el-option label="启用" :value="1"></el-option>
                  <el-option label="禁用" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="创建时间" prop="add_time">
                <DatePickerRange
                  v-model:start="formData.startTime"
                  v-model:end="formData.endTime"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="getData">搜索</el-button>
            <el-button type="default" @click="resetForm">重置</el-button>
            <el-button type="success" @click="handleAdd">新增配置</el-button>
            <el-button type="warning" @click="handleExport">导出配置</el-button>
            <el-upload
              ref="uploadRef"
              action="/admin/special-need/import-config"
              :limit="1"
              :show-file-list="false"
              :on-success="handleImportSuccess"
              :before-upload="beforeImport"
              :on-error="handleImportError"
              accept=".json"
            >
              <el-button type="info">导入配置</el-button>
            </el-upload>
          </el-form-item>
        </el-form>
        
        <div class="unitCount">
          <div>
            共计：<span class="danger">{{ pagination.total }}</span> 条配置
          </div>
        </div>
        
        <div class="showTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            style="width: 100%"
            align="center"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" align="center" />
            <el-table-column prop="name" label="配置名称" min-width="150" align="center" />
            <el-table-column prop="type_text" label="配置类型" width="100" align="center" />
            <el-table-column prop="target_info" label="目标信息" min-width="120" align="center" />
            <el-table-column prop="field_text" label="字段名称" width="120" align="center" />
            <el-table-column prop="field_value" label="字段值" min-width="200" align="center" show-overflow-tooltip />
            <el-table-column prop="platform_text" label="适用平台" width="100" align="center" />
            <el-table-column prop="status_text" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status_text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="active_status_text" label="生效状态" width="100" align="center" />
            <el-table-column prop="created_by_name" label="创建人" width="100" align="center" />
            <el-table-column prop="add_time" label="创建时间" width="150" align="center" />
            <el-table-column label="操作" fixed="right" width="200" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleView(row.id)">查看</el-button>
                <el-button type="success" size="small" @click="handleEdit(row.id)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            v-model="checkAll"
            label="全选"
            class="mr-10"
            @change="handleCheckAllChange"
            :indeterminate="isIndeterminate"
          ></el-checkbox>
          <el-select
            v-model="batchValue"
            placeholder="批量操作"
            :disabled="!selection.length"
            @change="handleBatchOperation"
            clearable
          >
            <el-option label="批量启用" value="enable" />
            <el-option label="批量禁用" value="disable" />
          </el-select>
        </div>
      </div>
      <Paging :total="pagination.total" @change="changePage"></Paging>
    </div>
  </div>
</template>
