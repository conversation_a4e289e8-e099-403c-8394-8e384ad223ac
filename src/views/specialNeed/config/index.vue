<template>
  <div class="main">
    <div class="formbigbox">
      <div class="formbox">
        <el-form
          ref="form"
          :model="formData"
          label-width="100px"
          class="demo-form-inline"
          :inline="true"
        >
          <el-row>
            <el-col :span="6">
              <el-form-item label="配置名称" prop="name">
                <el-input
                  v-model="formData['SpecialNeedConfig[name]']"
                  placeholder="请输入配置名称"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="配置类型" prop="type">
                <el-select
                  v-model="formData['SpecialNeedConfig[type]']"
                  placeholder="全部"
                  clearable
                >
                  <el-option label="职位信息" value="job"></el-option>
                  <el-option label="公告信息" value="announcement"></el-option>
                  <el-option label="单位信息" value="company"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="目标ID" prop="target_id">
                <el-input
                  v-model="formData['SpecialNeedConfig[target_id]']"
                  placeholder="请输入目标ID"
                  clearable
                  @keyup.enter="getData"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="状态" prop="status">
                <el-select
                  v-model="formData['SpecialNeedConfig[status]']"
                  placeholder="全部"
                  clearable
                >
                  <el-option label="启用" :value="1"></el-option>
                  <el-option label="禁用" :value="0"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item label="创建时间" prop="add_time">
                <DatePickerRange
                  v-model:start="formData.startTime"
                  v-model:end="formData.endTime"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item>
            <el-button type="primary" @click="getData">搜索</el-button>
            <el-button type="default" @click="resetForm">重置</el-button>
            <el-button type="success" @click="handleAdd">新增配置</el-button>
            <el-button type="warning" @click="handleExport">导出配置</el-button>
            <el-upload
              ref="uploadRef"
              action="/admin/special-need/import-config"
              :limit="1"
              :show-file-list="false"
              :on-success="handleImportSuccess"
              :before-upload="beforeImport"
              :on-error="handleImportError"
              accept=".json"
            >
              <el-button type="info">导入配置</el-button>
            </el-upload>
          </el-form-item>
        </el-form>

        <div class="unitCount">
          <div>
            共计：<span class="danger">{{ pagination.total }}</span> 条配置
          </div>
        </div>

        <div class="showTable">
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            style="width: 100%"
            align="center"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="id" label="ID" width="80" align="center" />
            <el-table-column prop="name" label="配置名称" min-width="150" align="center" />
            <el-table-column prop="type_text" label="配置类型" width="100" align="center" />
            <el-table-column prop="target_info" label="目标信息" min-width="120" align="center" />
            <el-table-column prop="field_text" label="字段名称" width="120" align="center" />
            <el-table-column
              prop="field_value"
              label="字段值"
              min-width="200"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column prop="platform_text" label="适用平台" width="100" align="center" />
            <el-table-column prop="status_text" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status_text }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              prop="active_status_text"
              label="生效状态"
              width="100"
              align="center"
            />
            <el-table-column prop="created_by_name" label="创建人" width="100" align="center" />
            <el-table-column prop="add_time" label="创建时间" width="150" align="center" />
            <el-table-column label="操作" fixed="right" width="200" align="center">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
                <el-button type="success" size="small" @click="handleEdit(row.id)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row.id)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>

    <div
      id="bottom-container"
      v-show="pagination.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            v-model="checkAll"
            label="全选"
            class="mr-10"
            @change="handleCheckAllChange"
            :indeterminate="isIndeterminate"
          ></el-checkbox>
          <el-select
            v-model="batchValue"
            placeholder="批量操作"
            :disabled="!selection.length"
            @change="handleBatchOperation"
            clearable
          >
            <el-option label="批量启用" value="enable" />
            <el-option label="批量禁用" value="disable" />
          </el-select>
        </div>
      </div>
      <Paging :total="pagination.total" @change="changePage"></Paging>
    </div>

    <!-- 配置弹窗 -->
    <ConfigDialog
      v-model:visible="dialogVisible"
      :edit-id="editId"
      @success="handleDialogSuccess"
    />

    <!-- 详情弹窗 -->
    <DetailDialog v-model:visible="detailVisible" :detail-data="detailData" />
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getConfigList,
  deleteConfig,
  batchUpdateConfigStatus,
  exportConfig
} from '/@/api/specialNeed'
import Paging from '/@/components/base/paging.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import ConfigDialog from './components/ConfigDialog.vue'
import DetailDialog from './components/DetailDialog.vue'

// 表单数据
const formData = reactive({
  'SpecialNeedConfig[name]': '',
  'SpecialNeedConfig[type]': '',
  'SpecialNeedConfig[target_id]': '',
  'SpecialNeedConfig[status]': '',
  startTime: '',
  endTime: '',
  page: 1,
  'per-page': 20
})

// 表格数据
const tableData = ref([])
const loading = ref(false)
const pagination = reactive({
  total: 0
})

// 选择相关
const selection = ref<any[]>([])
const checkAll = ref(false)
const batchValue = ref('')
const isIndeterminate = computed(() => {
  return selection.value.length > 0 && selection.value.length < tableData.value.length
})

// 上传相关
const uploadRef = ref()

// 弹窗相关
const dialogVisible = ref(false)
const editId = ref<number | string>()
const detailVisible = ref(false)
const detailData = ref<any>({})

// 获取数据
const getData = async () => {
  loading.value = true
  try {
    const params: any = { ...formData }
    if (params.startTime && params.endTime) {
      params['SpecialNeedConfig[add_time]'] = `${params.startTime} - ${params.endTime}`
    }
    delete params.startTime
    delete params.endTime

    const res = await getConfigList(params)
    tableData.value = res.items || []
    pagination.total = res.pagination?.totalCount || 0
  } catch (error) {
    console.error('获取配置列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  Object.keys(formData).forEach((key) => {
    if (key !== 'page' && key !== 'per-page') {
      formData[key] = ''
    }
  })
  formData.page = 1
  getData()
}

// 分页
const changePage = (pageInfo: any) => {
  formData.page = pageInfo.page
  formData['per-page'] = pageInfo.limit
  getData()
}

// 新增
const handleAdd = () => {
  editId.value = undefined
  dialogVisible.value = true
}

// 查看
const handleView = (row: any) => {
  detailData.value = row
  detailVisible.value = true
}

// 编辑
const handleEdit = (id: number) => {
  editId.value = id
  dialogVisible.value = true
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  getData()
}

// 删除
const handleDelete = async (id: number) => {
  try {
    await ElMessageBox.confirm('确定要删除这条配置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await deleteConfig({ id })
    ElMessage.success('删除成功')
    getData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
    }
  }
}

// 选择变化
const handleSelectionChange = (val: any[]) => {
  selection.value = val
  checkAll.value = val.length === tableData.value.length
}

// 全选变化
const handleCheckAllChange = (val: boolean) => {
  // 这里需要触发表格的全选/取消全选
  // 由于没有直接的API，可以通过其他方式实现
}

// 批量操作
const handleBatchOperation = async (operation: string) => {
  if (!selection.value.length) {
    ElMessage.warning('请先选择要操作的配置')
    return
  }

  const ids = selection.value.map((item: any) => item.id)
  const status = operation === 'enable' ? 1 : 0
  const actionText = operation === 'enable' ? '启用' : '禁用'

  try {
    await ElMessageBox.confirm(`确定要批量${actionText}选中的配置吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await batchUpdateConfigStatus({ ids, status })
    ElMessage.success(`批量${actionText}成功`)
    batchValue.value = ''
    getData()
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`批量${actionText}失败:`, error)
    }
  }
}

// 导出配置
const handleExport = async () => {
  try {
    const res = await exportConfig()
    // 创建下载链接
    const blob = new Blob([res], { type: 'application/json' })
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `special_need_config_${new Date().getTime()}.json`
    link.click()
    window.URL.revokeObjectURL(url)
    ElMessage.success('导出成功')
  } catch (error) {
    console.error('导出失败:', error)
  }
}

// 导入前检查
const beforeImport = (file: File) => {
  const isJSON = file.type === 'application/json' || file.name.endsWith('.json')
  if (!isJSON) {
    ElMessage.error('只能上传JSON格式的文件')
    return false
  }
  return true
}

// 导入成功
const handleImportSuccess = (response: any) => {
  ElMessage.success(response.message || '导入成功')
  getData()
}

// 导入失败
const handleImportError = (error: any) => {
  console.error('导入失败:', error)
  ElMessage.error('导入失败')
}

onMounted(() => {
  getData()
})
</script>

<style scoped lang="scss">
.main {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.formbigbox {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.formbox {
  .demo-form-inline {
    .el-form-item {
      margin-bottom: 15px;
    }
  }
}

.unitCount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px 0 15px 0;

  .danger {
    color: #f56c6c;
    font-weight: bold;
  }
}

.showTable {
  .el-table {
    border-radius: 4px;
    overflow: hidden;
  }
}

#bottom-container {
  background-color: #fff;
  padding: 15px 20px;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pt-15 {
  padding-top: 15px;
}

.mt-15 {
  margin-top: 15px;
}

.mr-10 {
  margin-right: 10px;
}

.jc-between {
  display: flex;
  justify-content: space-between;
}

.ai-center {
  display: flex;
  align-items: center;
}
</style>
