<template>
  <div class="filter-container-template">
    <el-form class="filter-grid-6" ref="form" :model="formData">
      <el-form-item label="职位检索" prop="name">
        <el-input
          v-model="formData.name"
          placeholder="请填写职位名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="公告检索" prop="announcement">
        <el-input
          v-model="formData.announcement"
          placeholder="请填写公告名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="单位检索" prop="company">
        <el-input
          v-model="formData.company"
          placeholder="请填写单位名称或ID"
          @keyup.enter="handleSearch"
          clearable
        ></el-input>
      </el-form-item>
      <el-form-item label="需求专业" prop="majorId">
        <MajorCategory :deep="2" :multiple="false" v-model="formData.majorId" placeholder="不限" />
      </el-form-item>
      <el-form-item label="学历要求" prop="educationType">
        <Education multiple v-model="formData.educationType" placeholder="不限" :isLimit="true" />
      </el-form-item>
      <el-form-item label="工作城市" prop="city">
        <Region multiple collapse-tags v-model="formData.city" placeholder="不限" />
        <!-- <el-input v-model="formData.city" placeholder="请输入城市"></el-input> -->
      </el-form-item>

      <template v-if="showMore">
        <el-form-item label="职位类型" prop="jobCategoryId">
          <JobCategory multiple v-model="formData.jobCategoryId" placeholder="不限" />
        </el-form-item>
        <el-form-item label="工作性质" prop="natureType">
          <WorkNature v-model="formData.natureType" placeholder="不限" />
        </el-form-item>
        <el-form-item label="创建时间" prop="addTimeStart">
          <DatePickerRange
            v-model:start="formData.addTimeStart"
            v-model:end="formData.addTimeEnd"
          />
        </el-form-item>
        <el-form-item label="发布时间" prop="releaseTimeStart">
          <DatePickerRange
            v-model:start="formData.releaseTimeStart"
            v-model:end="formData.releaseTimeEnd"
          />
        </el-form-item>
        <el-form-item label="刷新时间" prop="refreshTimeStart">
          <DatePickerRange
            v-model:start="formData.refreshTimeStart"
            v-model:end="formData.refreshTimeEnd"
          />
        </el-form-item>
        <el-form-item label="用人部门" prop="department">
          <el-input
            v-model="formData.department"
            placeholder="请填写用人部门"
            @keyup.enter="handleSearch"
            clearable
          ></el-input>
        </el-form-item>
        <el-form-item label="海外经历" prop="abroadType">
          <AbroadExperience v-model="formData.abroadType" placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item label="工作经验" prop="experienceType">
          <WorkExperience v-model="formData.experienceType" placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item label="职称要求" prop="titleType">
          <LevelTitle v-model="formData.titleType" multiple placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item label="审核状态" prop="auditStatus">
          <el-select v-model="formData.auditStatus" placeholder="不限">
            <el-option
              v-for="(item, index) in (auditOption as any)"
              :key="index"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="招聘状态" prop="status">
          <el-select v-model="formData.status" placeholder="不限">
            <el-option
              v-for="(item, index) in (jobOption as any)"
              :key="index"
              :label="item.v"
              :value="item.k"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="年龄要求" prop="ageType">
          <Age
            v-model="formData.ageType"
            placeholder="不限"
            :isLimit="true"
            @keyup.enter="handleSearch"
            clearable
          />
        </el-form-item>

        <el-form-item label="政治面貌" prop="politicalType">
          <Political v-model="formData.politicalType" placeholder="不限" :isLimit="true" />
        </el-form-item>
        <el-form-item label="发布模式" prop="isArticle">
          <ReleasePattern v-model="formData.isArticle" placeholder="不限" />
        </el-form-item>
        <el-form-item label="创 建 人" prop="creator">
          <el-input
            v-model="formData.creator"
            placeholder="请填写创建人"
            @keyup.enter="handleSearch"
            clearable
          ></el-input>
        </el-form-item>

        <el-form-item label="投递方式" prop="deliveryWay">
          <ResumeSource v-model="formData.deliveryWay" />
        </el-form-item>

        <el-form-item label="投递类型" prop="deliveryType">
          <DeliveryType v-model="formData.deliveryType" />
        </el-form-item>
        <el-form-item label="初始发布时间" prop="firstReleaseTimeStart">
          <DatePickerRange
            v-model:start="formData.firstReleaseTimeStart"
            v-model:end="formData.firstReleaseTimeEnd"
          />
        </el-form-item>
        <el-form-item label="是否小程序" prop="isMiniapp">
          <IsMiniapp v-model="formData.isMiniapp" />
        </el-form-item>
        <el-form-item label="职位联系人" prop="contact">
          <el-input v-model="formData.contact" placeholder="请填写账号ID/邮箱/手机号" />
        </el-form-item>

        <el-form-item label="协同子账号" prop="contactSynergy">
          <el-input v-model="formData.contactSynergy" placeholder="请填写子账号ID/邮箱/手机号" />
        </el-form-item>

        <el-form-item label="编制类型" prop="establishmentType">
          <EstablishmentType v-model="formData.establishmentType" />
        </el-form-item>
      </template>
    </el-form>

    <div class="filter-tools">
      <el-switch
        v-model="simple"
        size="large"
        active-text="简版(只支持部分搜索条件和显示结果)"
        inactive-text="全功能"
      />

      <div class="filter-btn-group">
        <el-button type="primary" @click="handleSearch">搜索</el-button>
        <el-button @click="handleResetField">重置</el-button>
        <el-button @click="handleDownload">下载</el-button>
        <el-link :underline="false" class="show" @click="showMore = !showMore">{{
          showMore ? '收起' : '展开'
        }}</el-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, nextTick, computed } from 'vue'
import { useRoute } from 'vue-router'
import MajorCategory from '/@select/majorCategory.vue'
import Education from '/@select/education.vue'
import Region from '/@select/region.vue'
import JobCategory from '/@select/jobCategory.vue'
import WorkNature from '/@select/workNature.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import WorkExperience from '/@select/workExperience.vue'
import LevelTitle from '/@select/levelTitle.vue'
import Age from '/@select/age.vue'
import Political from '/@select/political.vue'
import ReleasePattern from '/@select/jobReleasePattern.vue'
import DeliveryType from '/@/components/base/select/deliveryType.vue'
import ResumeSource from '/@/components/base/select/resumeSource.vue'
import IsMiniapp from '/@/components/base/select/isMiniapp.vue'
import EstablishmentType from '/@/components/base/select/establishmentType.vue'
import { getJobAuditStatusList, getJobStatusList } from '/@/api/job'

const emit = defineEmits(['search', 'download', 'update:isSimple'])
const props = defineProps({
  isSimple: Boolean
})
const form = ref()
const route = useRoute()

const showMore = ref(false)
const simple = computed({
  get() {
    return props.isSimple
  },
  set(val) {
    emit('update:isSimple', val)
  }
})
const auditOption = ref([])
const jobOption = ref([])

const formData = reactive({
  name: '',
  announcement: '',
  company: '',
  majorId: '',
  educationType: [],
  city: [],
  jobCategoryId: [],
  natureType: '',
  addTimeStart: '',
  addTimeEnd: '',
  releaseTimeStart: '',
  releaseTimeEnd: '',
  refreshTimeStart: '',
  refreshTimeEnd: '',
  department: '',
  abroadType: '',
  experienceType: '',
  titleType: '',
  auditStatus: '',
  status: '',
  ageType: '',
  sexType: '',
  politicalType: '',
  isArticle: '',
  creator: '',
  deliveryWay: '',
  deliveryType: '',
  firstReleaseTimeStart: '',
  firstReleaseTimeEnd: '',
  isMiniapp: '',
  contact: '',
  contactSynergy: '',
  establishmentType: [],
  export: 0
})
const params = reactive({
  announcement: route.query.id
})

const arrayToStringData = (data: any) => {
  return {
    ...data,
    city: data.city.join(),
    jobCategoryId: data.jobCategoryId.join(),
    educationType: data.educationType.join(),
    establishmentType: data.establishmentType?.join(),
    majorId: data.majorId instanceof Array ? data.majorId.join() : data.majorId
  }
}

const handleResetField = () => {
  form.value.resetFields()
  nextTick(() => {
    emit('search', arrayToStringData(formData))
  })
}

const handleDownload = () => {
  formData.export = 1
  emit('search', formData)
}

const handleSearch = () => {
  formData.export = 0
  formData.announcement = <string>params.announcement || formData.announcement
  emit('search', arrayToStringData(formData))
  params.announcement = ''
}

const getFilterOption = async () => {
  const [auditStatus, jobStatus] = <any>(
    await Promise.all([getJobAuditStatusList(), getJobStatusList()])
  )
  auditOption.value = auditStatus
  jobOption.value = jobStatus
}

onMounted(() => {
  getFilterOption()
})
</script>

<style scoped lang="scss">
.show {
  margin-left: 10px;
  white-space: nowrap;
  color: var(--color-primary);
}
</style>
