<template>
  <el-dialog
    v-model="visible"
    v-loading="loading"
    v-model:title="title"
    width="40%"
    top="50px"
    @close="cancelButton"
  >
    <el-form ref="formVm" :model="formData" :rules="formRules" class="form-data">
      <el-form-item prop="inviteDeliveryWay" label="邀约方式:" :label-width="formLabelWidth">
        <el-select
          v-model="formData.inviteDeliveryWay"
          placeholder="请选择邀约方式"
          @change="inviteDeliveryWayChange"
          class="select-width"
        >
          <el-option label="手动邀约" value="1" />
          <el-option label="智能邀约" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item
        class="invite-select"
        prop="inviteSelect"
        label="邀约文案:"
        :label-width="formLabelWidth"
      >
        <el-select
          v-model="formData.inviteSelect"
          placeholder="请选择邀约文案"
          @change="inviteSelectChange"
          class="select-width"
        >
          <el-option
            v-for="item in inviteSelectList"
            :key="item.name"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="inviteSelectText" label="" :label-width="formLabelWidth">
        <el-input
          class="invite-text textarea-text"
          :disabled="isEditInviteSelectText"
          v-model="formData.inviteSelectText"
          type="textarea"
          rows="2"
          placeholder="请阅读右侧提示，再自定义文本内容....."
        />
        <el-tooltip placement="right">
          <template #content>
            定义替换标签内容:<br />
            注意点1:大花括号必须包含<br />
            注意点2:花括号内容(小写英文字母)按下面要求填入。<br />
            1、{c} 替换 单位名称。<br />
            2、{u} 替换 人才姓名。<br />
            3、{j} 替换 职位名称。<br />
          </template>
          <el-icon :size="18" class="p-s-icon">
            <i class="el-icon-warning-outline"></i>
          </el-icon>
        </el-tooltip>
      </el-form-item>
      <el-form-item
        label="邀约人才:"
        :label-width="formLabelWidth"
        v-if="formData.inviteDeliveryWay === '1'"
        prop="invitePersonResumeIds"
      >
        <el-input
          class="invite-person-ids-text"
          v-model="formData.invitePersonResumeIds"
          placeholder="请填写人才id，多个用“、”隔开"
          type="textarea"
        />
      </el-form-item>
      <el-form-item
        label="人才匹配项:"
        :label-width="formLabelWidth"
        prop="personMatchType"
        v-if="formData.inviteDeliveryWay === '2'"
      >
        <el-col>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="1"
              name="personMatchType"
              @change="educationChange"
              >学历
            </el-checkbox>
            <el-row class="p-m-select">
              <Education
                :disabled="educationDisabled"
                v-model="formData.educationType"
                :data="true"
                :is_limit="false"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="2"
              name="personMatchType"
              @change="majorChange"
              >专业
            </el-checkbox>
            <el-row class="p-m-select">
              <MajorCategory
                :disabled="majorDisabled"
                :deep="3"
                class="select-width"
                v-model="formData.majorId"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="3"
              name="personMatchType"
              @change="jobCategoryChange"
              >意向职位
            </el-checkbox>
            <el-row class="p-m-select">
              <JobCategory
                :disabled="jobCategoryDisabled"
                :multiple="false"
                v-model="formData.jobCategoryId"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="4"
              name="personMatchType"
              @change="regionChange"
              >意向城市
            </el-checkbox>
            <el-row class="p-m-select">
              <Region
                :disabled="regionDisabled"
                v-model="regionValue"
                v-model:province="formData.provinceId"
                v-model:city="formData.cityId"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="5"
              name="personMatchType"
              @change="experienceChange"
            >
              工作经验
            </el-checkbox>
            <el-row class="p-m-select">
              <Experience
                :disabled="experienceDisabled"
                v-model="formData.experienceType"
                placeholder="请选择经验"
                is-limit="true"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="6"
              name="personMatchType"
              @change="titleChange"
            >
              职称
            </el-checkbox>
            <el-row class="p-m-select">
              <LevelTitle
                :disabled="titleDisabled"
                v-model="formData.titleType"
                placeholder="请选择职称"
                is-limit="true"
              />
            </el-row>
          </el-row>
          <el-row class="p-m-common">
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="7"
              name="personMatchType"
              @change="politicalChange"
            >
              政治面貌
            </el-checkbox>
            <el-row class="p-m-select">
              <Political
                :disabled="politicalDisabled"
                v-model="formData.politicalType"
                placeholder="请选择政治面貌"
                is-limit="true"
              />
            </el-row>
          </el-row>
          <el-row>
            <el-checkbox
              v-model="formData.personMatchType"
              class="p-match-checkbox"
              label="8"
              name="personMatchType"
              @change="abroadChange"
            >
              海外经验
            </el-checkbox>
            <el-row class="p-m-select">
              <AbroadExperience
                :disabled="abroadDisabled"
                v-model="formData.abroadType"
                placeholder="请选择海外经历"
                is-limit="true"
              />
            </el-row>
          </el-row>
        </el-col>
      </el-form-item>
      <el-form-item label="人才过滤项:" :label-width="formLabelWidth" prop="personSearchType">
        <el-checkbox-group v-model="formData.personSearchType">
          <el-row>
            <el-checkbox class="p-search-checkbox-item" label="1" name="personSearchType"
              >30天内已邀约过该职位
            </el-checkbox>
            <el-checkbox class="p-search-checkbox-item" label="2" name="personSearchType"
              >30天内已投递过该职位
            </el-checkbox>
          </el-row>
          <el-row>
            <el-checkbox class="p-search-checkbox-item" label="3" name="personSearchType"
              >简历完善度不足65%
            </el-checkbox>
            <el-checkbox class="p-search-checkbox-item" label="4" name="personSearchType"
              >30天内已被邀约过
            </el-checkbox>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item
        label="人才活跃时间:"
        :label-width="formLabelWidth"
        prop="personActiveDayNumber"
        v-if="formData.inviteDeliveryWay === '2'"
      >
        <el-row>
          <span>近</span>
          <el-input class="p-active-time" v-model="formData.personActiveDayNumber" type="text" />
          <span>天活跃</span>
        </el-row>
      </el-form-item>
      <el-form-item
        label="邀约人数:"
        :label-width="formLabelWidth"
        prop="inviteNumber"
        v-if="formData.inviteDeliveryWay === '2'"
      >
        <el-row>
          <el-input class="p-active-day" v-model="formData.inviteNumber" type="text" />
          <span>人</span>
        </el-row>
      </el-form-item>
      <el-form-item
        label="邀约时间:"
        :label-width="formLabelWidth"
        prop="inviteTime"
        v-if="formData.inviteDeliveryWay === '2'"
      >
        <el-date-picker
          v-model="formData.inviteTime"
          :disabledDate="handleDisabledDate"
          type="datetime"
          value-format="YYYY-MM-DD HH:mm:ss"
          format="YYYY-MM-DD HH:mm"
          placeholder="选择日期时间"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item
        label="备注:"
        :label-width="formLabelWidth"
        prop="remark"
        v-if="formData.inviteDeliveryWay === '2'"
      >
        <el-input class="remark textarea-text" v-model="formData.remark" type="textarea" />
      </el-form-item>
    </el-form>
    <el-dialog
      width="80%"
      top="20px"
      title="邀约检测提示"
      v-model="innerVisible"
      v-loading="innerLoading"
      append-to-body
    >
      <el-table ref="tableRef" :data="verifyList" style="width: 100%">
        <el-table-column
          type="selection"
          width="55"
          :selectable="
            (row) => {
              return row.isDisabled
            }
          "
        ></el-table-column>
        <el-table-column prop="resumeId" label="简历ID" width="120" />
        <el-table-column prop="name" label="名称" width="120" />
        <el-table-column prop="resumeDesc" label="基本信息" width="400" />
        <el-table-column prop="verifyNotice" label="验证提示信息" show-overflow-tooltip>
          <template #default="scope">
            <span v-if="scope.row.verifyNotice" class="notice-info">{{
              scope.row.verifyNotice
            }}</span>
            <span v-else>{{ scope.row.verifyNotice }}</span></template
          >
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="innerConfirmButton">继续邀约</el-button>
          <el-button @click="innerCancelButton"> 取消 </el-button>
        </span>
      </template>
    </el-dialog>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="confirmButton">确认</el-button>
        <el-button @click="cancelButton"> 取消 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import MajorCategory from '/@select/majorCategory.vue'
import JobCategory from '/@select/jobCategory.vue'
import Education from '/@select/education.vue'
import Region from '/@select/region.vue'
import Experience from '/@select/experience.vue'
import LevelTitle from '/@select/levelTitle.vue'
import Political from '/@select/political.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import { nextTick, reactive, ref, toRefs } from 'vue'
import { getInviteAdd, getInviteCheck, getInviteConfig } from '/@/api/job'
import { ElMessage } from 'element-plus'

export default {
  name: 'deliveryInvite',
  components: {
    MajorCategory,
    JobCategory,
    Education,
    Region,
    Experience,
    LevelTitle,
    Political,
    AbroadExperience
  },

  setup() {
    const formVm = ref()
    const tableRef = ref()
    const state = reactive({
      visible: false,
      innerVisible: false,
      loading: false,
      innerLoading: false,
      verifyPostData: <any>{},
      formLabelWidth: '120px',
      isEditInviteSelectText: true,
      educationDisabled: false,
      majorDisabled: false,
      jobCategoryDisabled: false,
      regionDisabled: false,
      experienceDisabled: true,
      titleDisabled: true,
      politicalDisabled: true,
      abroadDisabled: true,
      verifyList: [],
      inviteSelectList: [],
      inviteSelectTextList: {},
      title: '',
      jobInfo: <any>{},
      jobId: '',
      jobName: '',
      regionValue: <any>[],
      formData: {
        inviteDeliveryWay: '1',
        inviteSelect: 1,
        invitePersonResumeIds: '',
        inviteSelectText: '',
        educationType: [],
        experienceType: '',
        titleType: '',
        politicalType: '',
        abroadType: '',
        majorId: <any>[],
        personMatchType: ['1', '2', '3', '4'],
        personSearchType: [],
        jobCategoryId: '',
        provinceId: '',
        cityId: '',
        personActiveDayNumber: '',
        inviteNumber: '',
        remark: '',
        inviteTime: ''
      }
    })
    const formRules = ref({
      inviteDeliveryWay: [{ required: true }],
      inviteSelect: [{ required: true }],
      inviteSelectText: [
        { required: true, message: '邀约文案不允许为空', trigger: ['blur', 'change'] }
      ],
      invitePersonResumeIds: [
        { required: true, message: '邀约人才不允许为空', trigger: ['blur', 'change'] }
      ],
      personActiveDayNumber: [
        { required: true, message: '人才活跃时间不允许为空', trigger: ['blur', 'change'] }
      ],
      inviteNumber: [
        { required: true, message: '邀约人数不允许为空', trigger: ['blur', 'change'] }
      ],
      inviteTime: [{ required: true, message: '邀约时间不允许为空', trigger: ['blur', 'change'] }]
    })

    // 打开弹窗
    const open = (row: any) => {
      getConfig()
      state.visible = true
      state.jobInfo = row
      state.jobId = row.id
      state.jobName = row.name
      state.title = `投递邀约-当前操作职位ID为:${state.jobInfo.uid}`
    }

    // 关闭弹窗，并且数据初始化
    const cancelButton = () => {
      resetFields()
      state.visible = false
    }

    const confirmButton = () => {
      if (state.formData.inviteDeliveryWay === '1') {
        state.verifyPostData = {
          jobId: state.jobId,
          inviteDeliveryWay: state.formData.inviteDeliveryWay,
          inviteSelect: state.formData.inviteSelect,
          inviteSelectText: state.formData.inviteSelectText,
          invitePersonResumeIds: state.formData.invitePersonResumeIds,
          personSearchType: state.formData.personSearchType.join()
        }
      } else {
        state.verifyPostData = {
          jobId: state.jobId,
          inviteDeliveryWay: state.formData.inviteDeliveryWay,
          inviteSelect: state.formData.inviteSelect,
          inviteSelectText: state.formData.inviteSelectText,
          educationType: state.formData.educationType,
          experienceType: state.formData.experienceType,
          titleType: state.formData.titleType,
          politicalType: state.formData.politicalType,
          abroadType: state.formData.abroadType,
          provinceId: state.formData.provinceId,
          cityId: state.formData.cityId,
          jobCategoryId: state.formData.jobCategoryId,
          personActiveDayNumber: state.formData.personActiveDayNumber,
          inviteNumber: state.formData.inviteNumber,
          inviteTime: state.formData.inviteTime,
          remark: state.formData.remark,
          majorId: state.formData.majorId.join(),
          personMatchType: state.formData.personMatchType.join(),
          personSearchType: state.formData.personSearchType.join()
        }
      }
      checkResume(state.verifyPostData)
    }

    // 内确定按钮
    const innerConfirmButton = () => {
      const selectRows = tableRef.value.getSelectionRows()
      if (selectRows.length <= 0) {
        return ElMessage.error('至少勾选一个作为邀约对象')
      }
      const ResumeIds = <any>[]
      selectRows.forEach((row) => {
        ResumeIds.push(row.resumeId)
      })
      state.verifyPostData.invitePersonSelectResumeIds = ResumeIds.join(',')
      add(state.verifyPostData)
    }

    // 内部取消按钮
    const innerCancelButton = () => {
      state.innerVisible = false
    }

    // 投递方式change事件
    const inviteDeliveryWayChange = (val) => {
      resetFields()
      state.formData.inviteDeliveryWay = val
      state.formData.inviteSelectText = state.inviteSelectTextList[state.formData.inviteSelect]
      state.formData.personSearchType = []
      state.isEditInviteSelectText = true

      if (val === '2') {
        // 表单数据填充
        state.formData.educationType.push(state.jobInfo.educationType)
        state.formData.majorId = state.jobInfo.majorIds ? state.jobInfo.majorIds : []
        state.formData.provinceId = state.jobInfo.provinceId
        state.formData.cityId = state.jobInfo.cityId
        state.regionValue = [state.jobInfo.provinceId, state.jobInfo.cityId]
        state.formData.jobCategoryId = state.jobInfo.jobCategoryId
        state.formData.experienceType =
          state.jobInfo.experienceType !== '0' ? state.jobInfo.experienceType : '-1'
        state.formData.titleType = state.jobInfo.titleType !== '0' ? state.jobInfo.titleType : '-1'
        state.formData.politicalType =
          state.jobInfo.politicalType !== '0' ? state.jobInfo.politicalType : '-1'
        state.formData.abroadType =
          state.jobInfo.abroadType !== '0' ? state.jobInfo.abroadType : '-1'
      }
    }

    // 人才匹配项-学历change事件
    const educationChange = (val) => {
      state.educationDisabled = val <= 0
    }

    // 人才匹配项-专业change事件
    const majorChange = (val) => {
      state.majorDisabled = val <= 0
    }

    // 人才匹配项-职位类型change事件
    const jobCategoryChange = (val) => {
      state.jobCategoryDisabled = val <= 0
    }

    // 人才匹配项-地区change事件
    const regionChange = (val) => {
      state.regionDisabled = val <= 0
    }

    // 人才匹配项-工作经验change事件
    const experienceChange = (val) => {
      state.experienceDisabled = val <= 0
    }

    // 人才匹配项-职称change事件
    const titleChange = (val) => {
      state.titleDisabled = val <= 0
    }

    // 人才匹配项-政治面貌change事件
    const politicalChange = (val) => {
      state.politicalDisabled = val <= 0
    }

    // 人才匹配项-海外经验change事件
    const abroadChange = (val) => {
      state.abroadDisabled = val <= 0
    }

    // 邀请下拉change事件
    const inviteSelectChange = (val) => {
      state.formData.inviteSelectText = state.inviteSelectTextList[state.formData.inviteSelect]
      state.isEditInviteSelectText = val !== 66
    }

    // 重置表单数据
    const resetFields = () => {
      formVm.value.resetFields()
      state.isEditInviteSelectText = true
      state.educationDisabled = false
      state.majorDisabled = false
      state.jobCategoryDisabled = false
      state.regionDisabled = false
      state.experienceDisabled = true
      state.titleDisabled = true
      state.politicalDisabled = true
      state.abroadDisabled = true
    }

    // 获取配置文本
    const getConfig = async () => {
      await getInviteConfig().then((res: any) => {
        state.inviteSelectList = res.selectList
        state.inviteSelectTextList = res.selectTextList
        state.formData.inviteSelectText = res.selectTextList[state.formData.inviteSelect]
      })
    }

    // 检测简历
    const checkResume = async (formData) => {
      state.loading = true
      await getInviteCheck(formData)
        .then((res: any) => {
          state.verifyList = res.verifyList
          // 拉起检验弹窗
          state.innerVisible = true
          // 默认选中
          nextTick(() => {
            state.verifyList.forEach((row: any) => {
              tableRef.value.toggleRowSelection(row, row.isCheck)
            })
          })
        })
        .catch(() => {
          state.loading = false
        })
    }

    // 添加邀约配置
    const add = async (addData) => {
      state.innerLoading = true
      await getInviteAdd(addData)
        .then((res: any) => {
          // 关闭内层弹出窗
          state.innerVisible = false
          // 底层表单重置
          resetFields()
          // 关闭底层弹出窗
          state.visible = false
          // 提示成功
        })
        .catch(() => {
          state.innerLoading = false
        })
    }

    // 禁用时间
    const handleDisabledDate = (time: any) => {
      return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
    }

    return {
      formVm,
      tableRef,
      formRules,
      open,
      handleDisabledDate,
      cancelButton,
      confirmButton,
      innerConfirmButton,
      innerCancelButton,
      resetFields,
      inviteDeliveryWayChange,
      inviteSelectChange,
      educationChange,
      majorChange,
      jobCategoryChange,
      regionChange,
      experienceChange,
      titleChange,
      politicalChange,
      abroadChange,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped>
.form-data {
  margin-top: 10px;
}

.dialog-footer button:first-child {
  margin-right: 10px;
}

.invite-text {
  margin-top: 10px;
}

.select-width {
  width: 80%;
}

.invite-person-ids-text {
  width: 80%;
}

.p-match-checkbox {
  width: 20%;
}

.p-search-checkbox-item {
  width: 45%;
}

.textarea-text {
  width: 80%;
}

.p-active-time {
  width: 80px;
  margin: 0 5px;
}

.p-active-day {
  width: 100px;
  margin: 0 5px;
}

.p-m-common {
  margin-bottom: 5px;
}

.p-m-select {
  width: 60%;
}

.invite-select {
  margin-bottom: 0;
  padding-bottom: 0;
}

.p-s-icon {
  margin-left: 8px;
}

.notice-info {
  color: #ff0000;
}
</style>
