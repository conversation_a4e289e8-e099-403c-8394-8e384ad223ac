<template>
  <div class="box">
    <div id="top-container" class="pb-20">
      <div class="filter-container-template">
        <el-form class="filter-grid-6" ref="announcementForm" :model="formData">
          <el-form-item label="公告检索" prop="announcementTitleNum">
            <el-input
              placeholder="请填写公告标题或编号"
              v-model="formData.announcementTitleNum"
              clearable
              @keyup.enter="search"
            />
          </el-form-item>
          <el-form-item label="所属主栏目" prop="homeColumnId">
            <Colunm
              v-model="formData.homeColumnId"
              :columnList="columnList"
              multiple
              :check="false"
            />
          </el-form-item>
          <el-form-item label="公告属性" prop="notOverseasAttribute">
            <el-select
              v-model="formData.notOverseasAttribute"
              placeholder="不限"
              clearable
              filterable
            >
              <el-option
                v-for="item in notOverseasAttributeList"
                :key="item.k"
                :label="item.v"
                :value="item.k"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="学历要求" prop="educationType">
            <Education v-model="formData.educationType" />
          </el-form-item>
          <el-form-item label="编制类型" prop="establishmentType">
            <EstablishmentType v-model="formData.establishmentType" />
          </el-form-item>
          <el-form-item label="初发时间" prop="firstReleaseTimeStart" label-width="100px">
            <DatePickerRange
              v-model:start="formData.firstReleaseTimeStart"
              v-model:end="formData.firstReleaseTimeEnd"
            />
          </el-form-item>

          <div class="fill-template" v-show="showMore">
            <el-form-item label="职位检索" prop="name">
              <el-input
                placeholder="请填写职位名称或编号"
                v-model="formData.name"
                clearable
                @keyup.enter="search"
              />
            </el-form-item>
            <el-form-item label="职位类型" prop="jobCategoryId">
              <JobCategory v-model="formData.jobCategoryId" />
            </el-form-item>
            <el-form-item label="政治面貌" prop="politicalType">
              <Political v-model="formData.politicalType" />
            </el-form-item>
            <el-form-item label="职称要求" prop="titleType">
              <LevelTitle v-model="formData.titleType" />
            </el-form-item>
            <el-form-item label="工作经验" prop="experienceType">
              <WorkExperience v-model="formData.experienceType" />
            </el-form-item>
            <el-form-item label="海外经历" prop="abroadType">
              <AbroadExperience ispageSize v-model="formData.abroadType" />
            </el-form-item>

            <el-form-item label="工作地点" prop="city">
              <Region v-model="formData.city" />
            </el-form-item>
            <el-form-item label="投递方式" prop="deliveryWay">
              <ResumeSource v-model="formData.deliveryWay" multiple />
            </el-form-item>
            <el-form-item label="用人部门" prop="department">
              <el-input
                placeholder="请填写用人部门"
                v-model="formData.department"
                clearable
                @keyup.enter="search"
              />
            </el-form-item>
            <el-form-item label="需求专业" prop="majorId">
              <MajorCategory v-model="formData.majorId" :multiple="false" :deep="2" />
            </el-form-item>
            <el-form-item label="工作性质" prop="natureType">
              <WorkNature v-model="formData.natureType" />
            </el-form-item>
            <el-form-item label="刷新时间" prop="realRefreshTimeStart">
              <DatePickerRange
                v-model:start="formData.realRefreshTimeStart"
                v-model:end="formData.realRefreshTimeEnd"
              />
            </el-form-item>

            <el-form-item label="单位检索" prop="companyName">
              <el-input
                placeholder="请填写单位名称或编号"
                v-model="formData.companyName"
                clearable
                @keyup.enter="search"
              />
            </el-form-item>
            <el-form-item label="单位类型" prop="companyType">
              <CompanyType multiple v-model="formData.companyType" />
            </el-form-item>
            <el-form-item label="单位性质" prop="companyNature">
              <CompanyNature multiple v-model="formData.companyNature" />
            </el-form-item>
            <el-form-item label="是否小程序" prop="isMiniapp">
              <IsMiniapp v-model="formData.isMiniapp" />
            </el-form-item>
            <el-form-item label="所在栏目" prop="columnId">
              <Colunm
                v-model="formData.columnId"
                :columnList="columnList"
                multiple
                :check="false"
              />
            </el-form-item>
            <el-form-item label="发布时间" prop="refreshTimeStart">
              <DatePickerRange
                v-model:start="formData.refreshTimeStart"
                v-model:end="formData.refreshTimeEnd"
              />
            </el-form-item>

            <el-form-item label="创建人" prop="creator">
              <el-input
                placeholder="请填写创建人账号"
                v-model="formData.creator"
                clearable
                @keyup.enter="search"
              />
            </el-form-item>
            <el-form-item label="审核人" prop="">
              <el-input
                placeholder="请填写审核人"
                v-model="formData.auditAdminName"
                clearable
                @keyup.enter="search"
              />
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
              <el-select v-model="formData.auditStatus" placeholder="不限" clearable filterable>
                <el-option
                  v-for="item in auditStatusList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="招聘状态" prop="status">
              <el-select v-model="formData.status" placeholder="不限" filterable clearable>
                <el-option
                  v-for="item in statusRecruitList"
                  :key="item.k"
                  :label="item.v"
                  :value="item.k"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="显示状态" prop="isShow">
              <el-select v-model="formData.isShow" placeholder="请选择" clearable>
                <el-option label="-" :value="-1"> </el-option>
                <el-option label="显示" :value="1"> </el-option>
                <el-option label="隐藏" :value="2"> </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="年龄要求" prop="ageType">
              <Age v-model="formData.ageType" @keyup.enter="search" />
            </el-form-item>
          </div>
        </el-form>

        <div class="filter-tools">
          <div class="filter-tools-left">
            <slot name="left">
              <el-button type="primary" @click="checkDetails">+新增公告</el-button>
            </slot>
          </div>

          <div class="filter-btn-group">
            <slot name="right">
              <el-button type="primary" @click="search">搜索</el-button>
              <el-button @click="resetForm">重置</el-button>
              <el-link :underline="false" class="show" @click="handleShowMore">{{
                showMore ? '收起' : '展开'
              }}</el-link>
            </slot>
          </div>
        </div>

        <div class="jc-end amount">
          <el-link :underline="false" type="primary" size="small" @click="handleOpenCustomAnnounce"
            >选择列</el-link
          >
        </div>
      </div>
    </div>

    <el-table
      :data="announceData"
      ref="announce"
      :max-height="maxTableHeight"
      border
      style="width: 100%"
      align="center"
      v-loading="listLoading"
      @selection-change="handleAnnounceChange"
      @sort-change="handleSortTable"
    >
      <el-table-column type="selection" width="40" />

      <template v-for="item in customColumns">
        <el-table-column
          v-if="item.select"
          :key="item.key"
          :prop="item.prop"
          align="center"
          header-align="center"
          :label="item.label"
          :sortable="item.sortable"
          :min-width="setColumnMinWidth(item.key)"
          :fixed="item.key === 'operation' ? 'right' : false"
          :width="item.key === 'operation' ? '256px' : 'auto'"
        >
          <template #header v-if="item.headerSlot === 'sort'">
            {{ item.label }}
            <el-tooltip
              class="box-item"
              effect="dark"
              content="此排序仅对单位主页中的公告排序生效"
              placement="top"
            >
              <el-icon><QuestionFilled color="#909399" /></el-icon>
            </el-tooltip>
          </template>

          <template #header v-else-if="item.headerSlot === 'offlineDate'">
            {{ item.label }}
            <el-tooltip
              class="box-item"
              effect="dark"
              content="含已下线信息实际下线时间、在线信息预计下线时间、在线信息未配置下线时间情况"
              placement="top"
            >
              <el-icon><QuestionFilled color="#909399" /></el-icon>
            </el-tooltip>
          </template>

          <template v-if="item.slot === 'title'" #default="{ row }">
            <router-link
              class="bg-primary td-none"
              :to="`/cms/announcementDetail/${row.aid}/${row.status}`"
              >{{ row.title }}
            </router-link>
            <p class="tip red">
              {{ row.tip }}
            </p>
          </template>

          <template v-else-if="item.slot === 'companyName'" #default="{ row }">
            <el-button type="primary" link @click="companyDetails(row.companyId)">
              {{ row.companyName }}
            </el-button>
          </template>

          <template v-else-if="item.slot === 'sort'" #default="{ row }">
            <div class="sort ai-center">
              {{ row.homeSort }}
              <img
                @click="handleChangeSort(row)"
                class="sort-edit"
                src="/src/assets/icons/edit.svg"
                alt=""
              />
            </div>
          </template>

          <template v-else-if="item.slot === 'auditStatus'" #default="{ row }">
            <el-popover
              placement="top"
              :width="200"
              trigger="click"
              :content="row.opinion"
              v-if="row.auditStatus === '-1'"
            >
              <template #reference>
                <el-button type="primary" link>{{ row.auditStatusTxt }}</el-button>
              </template>
            </el-popover>
            <div v-else>{{ row.auditStatusTxt }}</div>
          </template>

          <template v-else-if="item.slot === 'linkNum'" #default="{ row }">
            <el-button type="primary" link @click="openApplyListDialog(row.aid)"
              >{{ row.jobOffApplyNum }}
            </el-button>
          </template>

          <template v-else-if="item.slot === 'isMiniappTxt'" #default="{ row }">
            <isMiniappChange
              :value="row.isMiniapp"
              type="announcement"
              :id="row.aid"
            ></isMiniappChange>
          </template>

          <template v-else-if="item.slot === 'operation'" #default="{ row }">
            <div class="table-button-group">
              <template v-if="row.btnList?.length < 6">
                <el-button
                  v-for="item in row.btnList"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>

                <el-popover placement="left" :width="20" trigger="click">
                  <template #reference>
                    <el-button
                      @click="getAttributeData(row.articleId)"
                      class="w100 mx-0 my-5"
                      plain
                      size="small"
                      >...</el-button
                    >
                  </template>
                  <div class="column btns">
                    <el-button
                      class="w100 mx-0 my-5 ml-0"
                      plain
                      size="small"
                      v-for="item in attributeData"
                      :key="item.type"
                      @click="refreshSortTime(item.type, item.articleId)"
                      >{{ item.typeTxt }}</el-button
                    >
                  </div>
                </el-popover>
              </template>

              <template v-else>
                <el-button
                  v-for="item in row.btnList?.slice(0, 5)"
                  :key="item.key"
                  size="small"
                  :disabled="item.disabled === 2"
                  :class="item.class"
                  @click="btnGroupEvent(item.key, row)"
                  >{{ item.label }}
                </el-button>

                <template v-if="row.btnList?.slice(5).length">
                  <el-popover placement="left" width="auto" trigger="click">
                    <template #reference>
                      <el-button class="white" size="small">更多</el-button>
                    </template>

                    <div class="table-popover-button">
                      <el-button
                        v-for="item in row.btnList?.slice(5)"
                        :key="item.key"
                        size="small"
                        :disabled="item.disabled === 2"
                        :class="item.class"
                        @click="btnGroupEvent(item.key, row)"
                        >{{ item.label }}
                      </el-button>

                      <el-popover placement="left" :width="20" trigger="click">
                        <template #reference>
                          <el-button
                            @click="getAttributeData(row.articleId)"
                            class="w100 mx-0 my-5"
                            plain
                            size="small"
                            >...</el-button
                          >
                        </template>
                        <div class="column btns">
                          <el-button
                            class="w100 mx-0 my-5 ml-0"
                            plain
                            size="small"
                            v-for="item in attributeData"
                            :key="item.type"
                            @click="refreshSortTime(item.type, item.articleId)"
                            >{{ item.typeTxt }}</el-button
                          >
                        </div>
                      </el-popover>
                    </div>
                  </el-popover>
                </template>
              </template>
            </div>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <div
      id="bottom-container"
      v-show="pages.total > 0"
      class="pt-15 jc-between ai-center"
      style="flex-shrink: 0"
    >
      <div class="mt-15 jc-between">
        <div class="ai-center">
          <el-checkbox
            v-model="checkAll"
            label="全选"
            class="mr-10"
            @change="announceChange"
            :indeterminate="isIndeterminate"
          ></el-checkbox>
          <el-select
            v-model="batchValue"
            placeholder="批量操作"
            :disabled="!announceSelection.length"
            @change="announceSelectionBatch"
            clearable
            filterable
          >
            <el-option v-for="item in batchOptions" :key="item.k" :label="item.v" :value="item.k" />
          </el-select>
        </div>
      </div>
      <Paging :total="pages.total" @change="changePage"></Paging>
    </div>

    <div class="mt-15 jc-between">
      <div class="ai-center"></div>
    </div>
  </div>
  <CustomColumnDialog ref="customAnnounceDialog" v-model:data="customColumns" />
  <AnnouncementBatchCopy ref="announcementBatchCopy" />
  <AnnouncementApplyListDialog ref="announcementApplyListDialog" />
  <AnnouncementAudit ref="announcementAudit" />
  <AnnouncementEdit ref="announcementEdit" :list="attributeDocument" />
  <JobDetailDialog ref="jobDetailDialog" />
  <SortChangeDialog ref="sortChangeDialog" />
  <router-view></router-view>
</template>

<script lang="ts">
export default {
  name: 'cmsAnnouncementList'
}
</script>

<script setup lang="ts">
import { QuestionFilled } from '@element-plus/icons-vue'
import { ref, onMounted, watch, nextTick } from 'vue'

import router from '/@/router'
import { useRoute } from 'vue-router'

import Paging from '/@/components/base/paging.vue'
import Region from '/@select/region.vue'
import { ElMessageBox } from 'element-plus'
import CustomColumnDialog from '/@/components/business/customColumnDialog-V2.vue'
import SortChangeDialog from '/src/components/business/sortChangeDialog.vue'
import EstablishmentType from '/@select/establishmentType.vue'
import MajorCategory from '/@select/majorCategory.vue'
import JobCategory from '/@select/jobCategory.vue'
import AnnouncementBatchCopy from '/@/components/dialog/announcementBatchCopy.vue'
import DatePickerRange from '/@/components/base/datePickerRange.vue'
import AnnouncementAudit from './component/announcementAudit.vue'
import AnnouncementEdit from './component/announcementEdit.vue'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'
import Colunm from '/@/components/base/colunm.vue'
import AnnouncementApplyListDialog from './component/announcementApplyListDialog.vue'
import IsMiniapp from '/@select/isMiniapp.vue'
import isMiniappChange from '/@select/isMiniappChange.vue'
import Education from '/@select/education.vue'
import Political from '/@select/political.vue'
import LevelTitle from '/@select/levelTitle.vue'
import WorkExperience from '/@select/workExperience.vue'
import AbroadExperience from '/@select/abroadExperience.vue'
import ResumeSource from '/@select/resumeSource.vue'
import WorkNature from '/@select/workNature.vue'
import CompanyType from '/@select/companyType.vue'
import CompanyNature from '/@select/companyNature.vue'
import Age from '/@select/age.vue'

import { getTableStagingField } from '/@/api/config'
// import { exportCooperationList } from '/@/api/unitManage'
import {
  announcementBatchDel,
  announcementBatchOnline,
  announcementBatchRefresh,
  announcementDelete,
  announcementIsShow,
  announcementOffLine,
  announcementRepublish,
  announcementRefresh,
  announcementRefreshSortTime,
  getAnnouncementAttributeData,
  getAnnouncementUnCooperationList,
  getAnnounceSearchParams,
  batchHidden,
  batchShow
} from '/@/api/announcement'

const showMore = ref(false)
const columnList = ref([])
const maxTableHeight = ref(450)

const formData = ref({
  announcementTitleNum: '',
  name: '',
  companyName: '',
  homeColumnId: '',
  auditStatus: '',
  jobCategoryId: '',
  status: '',
  majorId: '',
  educationType: '',
  city: [],
  department: '',
  attribute: '',
  creator: '',
  isShow: '',
  refreshTimeStart: '',
  refreshTimeEnd: '',
  realRefreshTimeStart: '',
  realRefreshTimeEnd: '',
  firstReleaseTimeStart: '',
  firstReleaseTimeEnd: '',
  page: '',
  limit: '',
  isArticle: 1,
  isMiniapp: '',
  establishmentType: [],
  notOverseasAttribute: '',
  overseasAttribute: '',

  politicalType: '',
  titleType: '',
  experienceType: '',
  abroadType: '',
  deliveryWay: '',
  natureType: '',
  companyType: '',
  companyNature: '',
  columnId: '',
  auditAdminName: '',
  ageType: ''
})

const announceData = <any>ref([])
const listLoading = ref(false)
const pages = ref({ currentPage: 1, size: 0, total: 0 })
const isDetail = ref(false)
const auditStatusList = <any>ref([])
const statusRecruitList = <any>ref([])
const attributeDocument = <any>ref([])
const notOverseasAttributeList = <any>ref([])
const batchValue = ref('')
const batchOptions = ref([
  { k: 1, v: '再发布文档' },
  { k: 2, v: '下线文档' },
  { k: 3, v: '刷新文档' },
  { k: 4, v: '编辑属性' },
  { k: 5, v: '复制文档' },
  { k: 6, v: '移动文档' },
  { k: 7, v: '删除文档' },
  { k: 8, v: '审核文档' },
  { k: 9, v: '显示文档' },
  { k: 10, v: '隐藏文档' }
])

const checkAll = ref(false)
const announceSelection = ref([])
const announceIds = ref('')
const attributeData = <any>ref([])

const announce = ref()
const route = useRoute()
const customAnnounceDialog = ref()
const announcementBatchCopy = ref()
const announcementAudit = ref()
const announcementEdit = ref()
const jobDetailDialog = ref()
const isIndeterminate = ref(false)
const customColumns = ref([
  {
    prop: 'announcementUuId',
    key: 'announcementUid',
    label: '公告ID',
    select: true,
    default: true
  },
  {
    prop: 'title',
    key: 'title',
    label: '公告标题',
    slot: 'title',
    select: true,
    default: true
  },
  {
    prop: 'companyName',
    key: 'companyName',
    label: '所属单位',
    slot: 'companyName',
    select: true,
    default: true
  },
  {
    prop: 'homeColumnTxt',
    key: 'column',
    label: '所属主栏目',
    select: true,
    default: true
  },
  {
    prop: 'allHomeColumnTxt',
    key: 'columnSub',
    label: '所属栏目',
    select: true,
    default: true
  },
  {
    prop: 'amountCount',
    key: 'amount',
    label: '招聘人数',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'allJobAmount',
    key: 'jobNum',
    label: '招聘职位',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'onlineJobAmount',
    key: 'onlineCount',
    label: '在线职位',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'offlineJobAmount',
    key: 'offlineCount',
    label: '已下线职位',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'amount',
    key: 'linkNum',
    label: '总投递次数',
    slot: 'linkNum',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'click',
    key: 'click',
    label: '点击量',
    sortable: 'custom',
    select: true,
    default: true
  },
  {
    prop: 'homeSort',
    key: 'sort',
    label: '排序',
    sortable: 'custom',
    slot: 'sort',
    headerSlot: 'sort',
    select: true,
    default: true
  },
  {
    prop: 'statusTxt',
    key: 'status',
    label: '招聘状态',
    select: false,
    default: false
  },
  {
    prop: 'auditStatusTxt',
    key: 'auditStatus',
    label: '审核状态',
    select: false,
    default: false
  },
  {
    prop: 'isShowTxt',
    key: 'isShow',
    label: '显示状态',
    select: false,
    default: false
  },
  {
    prop: 'addDate',
    key: 'addDate',
    label: '创建时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'firstReleaseTime',
    key: 'firstData',
    label: '初始发布时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'refreshTime',
    key: 'refreshTime',
    sortable: 'custom',
    label: '发布时间',
    select: false,
    default: false
  },
  {
    prop: 'realRefreshTime',
    key: 'realRefreshTime',
    label: '刷新时间',
    sortable: 'custom',
    select: false,
    default: false
  },
  {
    prop: 'offlineDate',
    key: 'offlineDate',
    label: '下线时间',
    sortable: 'custom',
    headerSlot: 'offlineDate',
    select: false,
    default: false
  },
  {
    prop: 'creatorName',
    key: 'creatorName',
    label: '创建人',
    select: false,
    default: false
  },
  {
    prop: 'audio',
    key: 'audio',
    label: '审核人',
    select: false,
    default: false
  },
  {
    prop: 'majorTxt',
    key: 'majorTxt',
    label: '学科专业',
    select: false,
    default: false
  },
  {
    prop: 'isMiniappTxt',
    key: 'isMiniappTxt',
    label: '是否小程序',
    select: false,
    default: false
  },
  {
    prop: 'operation',
    key: 'operation',
    label: '操作',
    slot: 'operation',
    disabled: true,
    select: true,
    default: true
  }
])

const announcementApplyListDialog = ref()
const sortChangeDialog = ref()

const getAnnounceSearch = (sort = {}) => {
  listLoading.value = true

  const params = {}
  Object.keys(formData.value).forEach((key: string) => {
    const value = formData.value[key]
    params[key] = Array.isArray(value) ? value.join() : value
  })

  getAnnouncementUnCooperationList({ ...params, ...sort }).then((res: any) => {
    announceData.value = res.list
    pages.value.size = res.page.limit
    pages.value.total = res.page.count
    listLoading.value = false
  })
}

onMounted(async () => {
  if (route.name === 'companyCooperationListDetail') {
    isDetail.value = true
  } else {
    isDetail.value = false
  }

  const data = await getTableStagingField('announcementCacheList')
  if (data.value) {
    const value = data.value.split(',')
    customColumns.value = customColumns.value.map((item: any) => {
      return {
        ...item,
        select: value.includes(item.key)
      }
    })
  }

  const resp = await getAnnounceSearchParams()
  const {
    columnList: column,
    notOverseasAttributeList: notOverseasAttribute,
    auditStatusList: auditStatus,
    statusRecruitList: statusRecruit
  } = resp

  notOverseasAttributeList.value = notOverseasAttribute
  auditStatusList.value = auditStatus
  statusRecruitList.value = statusRecruit
  columnList.value = column

  getAnnounceSearch()
})

watch(
  () => route.name,
  (n: any) => {
    if (n === 'companyCooperationListDetail') {
      isDetail.value = true
    } else {
      isDetail.value = false
    }
  }
)

const search = () => {
  getAnnounceSearch()
}

const announcementForm = ref()

const resetForm = () => {
  announcementForm.value.resetFields()
  getAnnounceSearch()
}

const checkDetails = () => {
  router.push({ name: 'cmsAnnouncementAdd' })
}

const changePage = (r: any) => {
  formData.value.page = r.page
  formData.value.limit = r.total
  getAnnounceSearch()
}

// const excel = () => {
//   exportCooperationList(formData.value).then((res) => {
//     window.location.href = res.downloadUrl
//   })
// }

const handleOpenCustomAnnounce = () => {
  customAnnounceDialog.value.open('announcementCacheList')
}

// const pushAudit = (id: any) => {
//   router.push(`/cms/announcementAuditDetail/${id}`)
// }

const deleteAnnounce = (id: string) => {
  ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    await announcementDelete({ id })
    announceData.value = announceData.value.filter((item: any) => item.aid !== id)
  })
}

interface announceOffline {
  id: number
  actionType: string
}

const announceIsShow = async (type: announceOffline) => {
  await announcementIsShow(type)
  getAnnounceSearch()
}

const handleAnnouncementOffline = async (announcementId) => {
  await announcementOffLine({ announcementId })
  getAnnounceSearch()
}

const handleAnnouncementRepublish = async (announcementId) => {
  await announcementRepublish({ announcementId })
  getAnnounceSearch()
}

const announceEdit = (id: any) => {
  router.push({ path: `/cms/announcementEdit/${id}` })
}

const handleAnnounceChange = (data: any) => {
  announceSelection.value = data
  announceIds.value = data.map((item: any) => item.aid).join()
  if (data.length === announceData.value.length) {
    checkAll.value = true
    isIndeterminate.value = false
  } else {
    checkAll.value = false
    isIndeterminate.value = data.length > 0
  }
}

const announceChange = () => {
  announce.value.toggleAllSelection()
}

const changeHideState = (changeState, type) => {
  ElMessageBox.confirm(`确定要${type === 1 ? '显示' : '隐藏'}文档吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const { content } = await changeState({ ids: announceIds.value })
    if (content) {
      ElMessageBox.alert(content, '提示', {
        dangerouslyUseHTMLString: true,
        showConfirmButton: false
      })
    }
    getAnnounceSearch()
  })
}

const announceSelectionBatch = async (val: any) => {
  batchValue.value = ''
  if (val === 9) {
    changeHideState(batchShow, 1)
  }
  if (val === 10) {
    changeHideState(batchHidden, 2)
  }
  if (val === 5 || val === 6) {
    announcementBatchCopy.value.openBatchCopy(val, announceIds.value)
  }
  if (val === 7) {
    ElMessageBox.confirm('此操作将永久删除选中的数据，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      await announcementBatchDel({ ids: announceIds.value })
      getAnnounceSearch()
    })
  }
  if (val === 8) {
    announcementAudit.value.open(announceIds.value)
  }
  if (val === 4) {
    announcementEdit.value.open(announceIds.value)
  }
  if (val === 1 || val === 2) {
    await announcementBatchOnline({ ids: announceIds.value, actionType: val })
    getAnnounceSearch()
  }
  if (val === 3) {
    await announcementBatchRefresh({ ids: announceIds.value })
    getAnnounceSearch()
  }
}

const handleSortTable = ({ prop, order }) => {
  // 倒序：descending， 正序：ascending
  // 1:倒序；2:正序
  const sortMap = {
    amountCount: 'sortAmountCount',
    allJobAmount: 'sortAllJobAmount',
    onlineJobAmount: 'sortOnlineJobAmount',
    offlineJobAmount: 'sortOfflineJobAmount',
    amount: 'sortAmount',
    click: 'sortClock',
    addDate: 'sortAddDate',
    firstReleaseTime: 'sortFirstReleaseTime',
    refreshTime: 'sortRefreshTime',
    realRefreshTime: 'sortRealRefreshTime',
    offlineDate: 'sortOfflineDate',
    homeSort: 'sortHomeSort'
  }

  const key = sortMap[prop]
  const sort = order === 'ascending' ? 2 : 1

  getAnnounceSearch({ [key]: sort })
}

const companyDetails = (id: string) => {
  router.push({
    path: '/company/details',
    query: { id }
  })
}

const openApplyListDialog = (id: any) => {
  router.push({
    path: '/announcement/business',
    query: { announcementId: id }
  })
}

const refresh = async (id: any) => {
  await announcementRefresh({ id })
  getAnnounceSearch()
}

const getAttributeData = async (articleId: string) => {
  attributeData.value = await getAnnouncementAttributeData({ articleId })
}

const refreshSortTime = async (attributeId: string, articleId: string) => {
  await announcementRefreshSortTime({ attributeId, articleId })
}

const setColumnMinWidth = (key: string) => {
  let minWidth = 90

  switch (key) {
    case 'column':
      minWidth = 110
      break
    case 'amount':
      minWidth = 110
      break
    case 'jobNum':
      minWidth = 110
      break
    case 'onlineCount':
      minWidth = 110
      break
    case 'offlineCount':
      minWidth = 130
      break
    case 'linkNum':
      minWidth = 130
      break

    case 'sort':
      minWidth = 110
      break
    case 'click':
      minWidth = 110
      break
    case 'addDate':
      minWidth = 140
      break
    case 'firstData':
      minWidth = 140
      break
    case 'refreshTime':
      minWidth = 110
      break
    case 'realRefreshTime':
      minWidth = 110
      break
    case 'offlineDate':
      minWidth = 130
      break
    case 'operation':
      minWidth = 210
      break
    case 'isMiniappTxt':
      minWidth = 110
      break

    default:
      minWidth = 90
      break
  }

  return minWidth
}

const handleChangeSort = (row) => {
  const { aid: id, homeSort: sort } = row
  const callback = (newSort) => {
    row.homeSort = newSort
  }
  sortChangeDialog.value.open({ id, sort }, callback)
}

const getTableHeight = async () => {
  await nextTick(() => {})
  const topHeight = document.getElementById('top-container')?.clientHeight || 0
  const height = Number(
    document.getElementById('layout-main')?.getAttribute('data-view-height') || 0
  )
  const bottomHeight = document.getElementById('bottom-container')?.clientHeight || 0
  // .box 内边框
  const padding = 40
  maxTableHeight.value = height - topHeight - bottomHeight - padding
}

const handleShowMore = () => {
  showMore.value = !showMore.value
  getTableHeight()
}

const btnGroupEvent = async (key: string, row: any) => {
  const { announcementId } = row

  switch (key) {
    // 刷新
    case 'refresh':
      await refresh(announcementId)
      break
    // 隐藏
    case 'hide':
      await announceIsShow({ id: row.aid, actionType: '2' })
      break
    // 显示
    case 'show':
      await announceIsShow({ id: announcementId, actionType: '1' })
      break
    // 编辑
    case 'edit':
      await announceEdit(announcementId)
      break
    // 下线
    case 'offline':
      await handleAnnouncementOffline(announcementId)
      break
    // 再发布
    case 'republish':
      await handleAnnouncementRepublish(announcementId)
      break
    // 删除
    case 'delete':
      await deleteAnnounce(announcementId)
      break

    default:
      break
  }

  getAnnounceSearch()
}
</script>
<style lang="scss" scoped>
.box {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px 15px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  a {
    text-decoration: none;
    color: var(--color-primary);
  }
}

.show {
  margin-left: 10px;
  white-space: nowrap;
  color: var(--color-primary);
}

.amount {
  margin: 20px 0;
  height: 30px;
  padding: 0 10px;
  line-height: 30px;
  background-color: #edf9ff;
}
.column {
  .my-5 {
    margin-left: auto;
  }
}
.tip {
  font-size: 12px;
}
.danger {
  color: #d9041a;
  font-weight: bold;
}
.sort {
  display: flex;
  align-items: center;
  justify-content: center;
  .sort-edit {
    width: 20px;
    opacity: 0.6;
    margin-left: 5px;
    cursor: pointer;
  }
}
</style>
