<template>
  <div>
    <el-dialog v-model="batchEditVisible" title="批量编辑属性" width="500px">
      <AnnouncementCheckBox v-model="form.attribute" :checkBoxList="list" />
      <el-button type="primary" @click="submit">确定</el-button>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref } from 'vue'
import { announcementBatchEdit } from '/@/api/announcement'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'

defineOptions({ name: 'announcementEdit' })

defineProps({
  list: {
    type: Array,
    default: () => []
  }
})

const batchEditVisible = ref(false)
const form = ref({
  ids: '',
  attribute: ''
})

// eslint-disable-next-line no-unused-vars
const open = (id: any) => {
  form.value.ids = id
  batchEditVisible.value = true
}

const submit = async () => {
  if (form.value.attribute === '') {
    ElMessage.error('请选择属性')
  } else {
    await announcementBatchEdit(form.value)
    batchEditVisible.value = false
    form.value.attribute = ''
  }
}
</script>

<style lang="scss" scoped></style>
