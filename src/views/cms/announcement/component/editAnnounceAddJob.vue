<template>
  <!-- 修改公告+新增职位 -->
  <div class="pb-10 border-bottom">
    <div class="fw-bold mb-10 color-dark">
      修改类型：{{ baseInfo.editorTypeTxt }}
      <router-link
        class="ml-10 bg-primary td-none"
        :to="`/cms/announcementDetail/${baseInfo.id}/${baseInfo.status}`"
        >预览原公告</router-link
      >
    </div>
    <div class="fw-bold mb-10 color-dark">修改前：</div>
    <div class="flex mb-10">
      <div class="detail-main" v-html="baseInfo.announcementHandleBefore"></div>
    </div>
    <div class="flex mb-10" v-if="baseInfo.fileHandleBefore?.length">
      <JobAttachment :fileList="baseInfo.fileHandleBefore" />
    </div>
    <div class="fw-bold mb-10 color-dark">修改后：</div>
    <div class="flex mb-10">
      <div class="detail-main" v-html="baseInfo.announcementHandleAfter"></div>
    </div>
    <div class="flex mb-10" v-if="baseInfo.fileHandleAfter?.length">
      <JobAttachment :fileList="baseInfo.fileHandleAfter" />
    </div>
    <div class="fw-bold mb-10 color-dark" id="job">新增职位详情：</div>
    <el-table :data="baseInfo.authJobList" border>
      <el-table-column label="岗位代码" prop="code" />
      <el-table-column label="职位名称" prop="name">
        <template #default="{ row, $index }">
          <el-link type="primary" :underline="false" @click="jobDetail(baseInfo.id, $index + 1)">{{
            row.name
          }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="基本信息" prop="information" width="300px" class-name="information">
        <template #default="{ row }">
          <el-tooltip
            popper-class="information-class"
            class="box-item"
            :content="row.information"
            placement="top-start"
          >
            {{ row.information }}
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column prop="department" label="用人部门" />
      <el-table-column prop="contact" label="职位联系人">
        <template #default="{ row }">
          <div class="flex">
            <div>{{ row.jobContact?.contact }}</div>
            <div class="ml-5">{{ row.jobContact?.companyMemberType === '0' ? '主' : '' }}</div>
          </div>
          <div>
            <span class="mr-5">{{ row.jobContact?.email }}</span>
            <span class="mr-5">{{ row.jobContact?.mobile }}</span>
            <span v-if="row.jobContact?.department">/{{ row.jobContact?.department }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="contactSynergyNum" label="协同子账号">
        <template #default="{ row }">
          <div v-if="row.jobContactSynergy === 0">{{ row.jobContactSynergy }}</div>
          <el-popover v-else placement="top-start" :width="200">
            <template #reference>
              {{ row.jobContactSynergyNum }}
            </template>
            <h4>协同子账号</h4>
            <div v-for="item in row.jobContactSynergy" :key="item.id">
              <div class="flex mt-5">
                <span class="color-danger" v-if="item.isContact === 1">联</span>
                <div>{{ item.contact }} / {{ item.department }}</div>
              </div>
              <div>{{ item.email }} {{ item.mobile }}</div>
            </div>
          </el-popover>
        </template>
      </el-table-column>
    </el-table>
    <JobDetailDialog ref="jobDetailDialog" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps } from 'vue'
import JobAttachment from './jobAttachment.vue'
import JobDetailDialog from '/@/components/job/jobDetailDialog.vue'

defineOptions({ name: 'editAnnounceAddJob' })

defineProps({
  baseInfo: {
    type: Object,
    default: () => ({})
  }
})

const jobDetailDialog = ref()
const jobDetail = (id: string, page: number) => {
  jobDetailDialog.value.open(id, page)
}
</script>

<style lang="scss" scoped>
@use '/src/theme/app' as *;

.border-bottom {
  :deep(.information) {
    .cell {
      @include utils-ellipsis-lines(2, 1.5);
    }
  }
}

.detail-main {
  :deep() {
    p {
      color: rgba(51, 51, 51, 0.8);
      font-size: 14px;
      line-height: 2;
    }

    img {
      display: block;
      margin: 20px auto;
      max-width: 100%;
    }

    table {
      margin: 20px auto;
      width: 100%;
      border-collapse: collapse;

      th {
        background-color: #fafafc;
      }

      th,
      td {
        padding: 10px 0;
        text-align: center;
        border: 1px solid #ccc;
      }
    }
  }
}
</style>

<style lang="scss">
.information-class {
  width: 250px;
}
</style>
