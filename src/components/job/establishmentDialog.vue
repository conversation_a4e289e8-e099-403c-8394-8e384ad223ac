<template>
  <el-dialog v-model="realVisible" title="职位编制" @close="handleClose">
    <el-form :model="formData">
      <el-radio-group v-model="formData.type" class="radio-box" @change="handleChange">
        <el-col><el-radio label="1">请选择编制类型</el-radio></el-col>
        <el-col class="radio-item-box">
          <announcement-check-box
            v-model="formData.set"
            :disabled="disabled"
            :check-box-list="list"
          />
        </el-col>

        <el-col><el-radio label="2">取消职位编制(即清空该字段原有设置)</el-radio></el-col>
      </el-radio-group>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="realVisible = false">取消</el-button>
        <el-button type="primary" @click="establishmentSubmit"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import AnnouncementCheckBox from '/@/components/base/announcementCheckBox.vue'
import { defineProps, reactive, ref, computed, defineEmits } from 'vue'

import { jobEstablishment, batchJobEstablishment } from '/@/api/job'
import { getJobEstablishmentList } from '/@/api/cmsJob'

const emits = defineEmits(['update:visible', 'update'])

const prop = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  isSimple: {
    type: Boolean,
    default: true
  },
  jobId: {
    type: String,
    default: ''
  }
})

const list = ref([])

const formData = reactive({
  type: '1',
  set: ''
})

const realVisible = computed({
  get() {
    return prop.visible
  },
  set(value) {
    emits('update:visible', value)
  }
})

const disabled = ref(false)

const handleClose = () => {
  formData.set = ''
  formData.type = '1'
}

const getJobEstablishment = () => {
  getJobEstablishmentList().then((resp: any) => {
    list.value = resp
  })
}
getJobEstablishment()

const handleChange = () => {
  formData.set = ''
  if (formData.type === '1') {
    disabled.value = false
  } else {
    disabled.value = true
  }
}

const establishmentSubmit = () => {
  if (formData.type === '1' && formData.set.length <= 0) {
    ElMessage({
      message: '请选择编制类型',
      type: 'warning',
      duration: 5000
    })
    return
  }

  const fetch = prop.isSimple ? jobEstablishment : batchJobEstablishment

  fetch({ jobId: prop.jobId, ...formData }).then(() => {
    realVisible.value = false
    emits('update')
  })
}
</script>

<style lang="scss" scoped></style>
