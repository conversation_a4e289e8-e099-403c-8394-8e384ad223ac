<template>
  <el-dialog v-model="visible" title="自定义显示列(最多12项)" width="750px">
    <div class="content">
      <div class="mb-20">选择显示列</div>
      <div class="wrap">
        <el-tag
          v-for="(item, index) in list"
          :class="{ checked: item.select, 'is-disabled': item.disabled }"
          :key="index"
          :disable-transitions="false"
          @click="handleTagClick(item)"
          >{{ item.label }}
        </el-tag>
      </div>
    </div>
    <template #footer>
      <div class="flex-end">
        <el-button :loading="btnLoading" size="small" type="primary" @click="handleConfirm"
          >确定</el-button
        >
        <el-button size="small" @click="handleClose">取消</el-button>
        <el-link class="fs-12 ml-10" type="primary" :underline="false" @click="handleDefault"
          >恢复默认项</el-link
        >
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { reactive, toRefs, watch } from 'vue'
import { ElMessage } from 'element-plus'

import { setTableStagingField } from '/@/api/config'

export default {
  name: 'customColumnDialog',
  props: {
    data: {
      type: Array,
      default() {
        return []
      }
    },
    min: {
      type: Number,
      default() {
        return 5
      }
    },
    max: {
      type: Number,
      default() {
        return 12
      }
    }
  },
  components: {},
  emits: ['update:data'],
  setup(props: any, { emit }) {
    const state = reactive({
      btnLoading: false,
      key: '',
      visible: false,
      list: <any>[]
    })

    watch(
      () => props.data,
      (val: any) => {
        state.list = JSON.parse(JSON.stringify(val))
      },
      { deep: true, immediate: true }
    )

    const open = (key: string) => {
      if (!key) {
        ElMessage.error(`key不存在`)
        return
      }
      const { data } = props
      state.list = JSON.parse(JSON.stringify(data))
      state.key = key
      state.visible = true
    }

    const checkedLength = (flag: Boolean) => {
      const { length } = state.list.filter((item: any) => item.select)
      const { min, max } = props
      if (length === min && flag === true) {
        ElMessage.error(`最少${min}项`)
        return false
      }
      // if (length === (props as any).max && flag === false) {
      //   ElMessage.error(`最多${max}项`)
      //   return false
      // }
      return true
    }

    const handleTagClick = (item: any) => {
      const { select, disabled } = item
      if (disabled) return
      const flag = checkedLength(select)
      if (!flag) return
      // eslint-disable-next-line no-param-reassign
      item.select = !item.select
    }

    const handleClose = () => {
      state.visible = false
    }

    const handleConfirm = () => {
      state.btnLoading = true
      const columns = JSON.stringify(state.list)
      const select = state.list.filter((item: any) => item.select)
      const k = select.map((item: any) => item.key).join()
      setTableStagingField(state.key, k)
        .then(() => {
          state.btnLoading = false
          handleClose()
          emit('update:data', JSON.parse(columns))
        })
        .catch(() => {
          state.btnLoading = false
        })
    }

    const handleDefault = () => {
      state.list = state.list.map((item: any) => {
        return {
          ...item,
          select: item.default
        }
      })
    }

    return {
      open,
      handleConfirm,
      handleClose,
      handleTagClick,
      handleDefault,
      ...toRefs(state)
    }
  }
}
</script>

<style scoped lang="scss">
.el-tag {
  margin-left: 10px;
  border-radius: 2px;
  min-width: 82px;
  text-align: center;
  font-size: 13px;
  height: 32px;
  line-height: 30px;
  margin-bottom: 10px;
  color: #666;
  border-color: #e1e1e1;
  background-color: transparent;
  cursor: pointer;
  &.checked {
    color: #fff;
    border-color: var(--color-primary);
    background-color: var(--color-primary);
  }
  &.checked.is-disabled {
    cursor: not-allowed;
    border-color: var(--color-primary-light-7);
    background-color: var(--color-primary-light-7);
  }
}
</style>
