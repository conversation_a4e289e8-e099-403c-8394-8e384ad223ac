import { RouteRecordRaw } from 'vue-router'

export const companyRoutes: Array<RouteRecordRaw> = [
  {
    path: '/company',
    name: 'company',
    component: () => import('/@/layout/routerView/parent.vue'),
    redirect: '/company/query',
    meta: {
      title: '单位管理',
      isLink: '',
      isHide: false,
      isKeepAlive: false,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/company.svg'
    },
    children: [
      {
        path: '/company/add',
        name: 'addCompany',
        component: () => import('/@/views/company/add/index.vue'),
        meta: {
          title: '新增单位',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/query',
        name: 'queryComany',
        component: () => import('/@/views/company/query/index.vue'),
        meta: {
          title: '单位查询',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/joinAudit',
        name: 'joinAudit',
        component: () => import('/@/views/company/joinAudit/index.vue'),
        meta: {
          title: '入网审核',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/joinAudit/auditDetails/:id',
        name: 'joinAuditDetails',
        component: () => import('/@/views/company/joinAudit/details.vue'),
        meta: {
          title: '审核详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: true,
          icon: ''
        }
      },
      {
        path: '/company/add/:id',
        name: 'editCompanyById',
        component: () => import('/@/views/company/add/index.vue'),
        meta: {
          title: '重新提交',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/edit',
        name: 'editCompany',
        component: () => import('/@/views/company/edit.vue'),
        meta: {
          title: '编辑单位',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/details',
        name: 'companyDetails',
        component: () => import('/@/views/company/details.vue'),
        meta: {
          title: '单位详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/account',
        name: 'accountList',
        component: () => import('/@/views/company/account/index.vue'),
        meta: {
          title: '账号查询',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/account/add',
        name: 'addAccount',
        component: () => import('/@/views/company/account/add.vue'),
        meta: {
          title: '创建子账号',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/account/setting/:id',
        name: 'accountSetting',
        component: () => import('/@/views/company/account/setting.vue'),
        meta: {
          title: '账号设置',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/account/log/:id',
        name: 'accountLog',
        component: () => import('/@/views/company/account/log.vue'),
        meta: {
          title: '账号日志',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/registered',
        name: 'companyRegisteredQuery',
        component: () => import('/@/views/company/registered/index.vue'),
        meta: {
          title: '注册查询',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/cooperationList',
        name: 'companyCooperationList',
        component: () => import('/@/views/company/cooperationList/index.vue'),
        meta: {
          title: '合作申请',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        },
        children: [
          {
            path: 'detail/:id',
            name: 'companyCooperationListDetail',
            component: () => import('/@/views/company/cooperationList/detail.vue'),
            meta: {
              title: '合作申请详情',
              isLink: '',
              isHide: true,
              isKeepAlive: false,
              isAffix: false,
              isIframe: true,
              icon: ''
            }
          }
        ]
      },
      {
        path: '/company/waitCooperationList',
        name: 'waitCooperationList',
        component: () => import('/@/views/company/waitCooperationList/index.vue'),
        meta: {
          title: '免费单位',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/company/waitCooperationList/add',
        name: 'addWaitCooperation',
        component: () => import('/@/views/company/waitCooperationList/add.vue'),
        meta: {
          title: '新增免费单位',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: true,
          icon: ''
        }
      },
      {
        path: '/company/waitCooperationList/details/:id',
        name: 'waitCooperationDetails',
        component: () => import('/@/views/company/waitCooperationList/details.vue'),
        meta: {
          title: '免费单位详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: true,
          icon: ''
        }
      },
      {
        path: '/company/interview',
        name: 'interview',
        component: () => import('/@/views/company/interview/index.vue'),
        meta: {
          title: '面试管理',
          isLink: '',
          isHide: false,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
