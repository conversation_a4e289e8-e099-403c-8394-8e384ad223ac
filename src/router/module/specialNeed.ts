import { RouteRecordRaw } from 'vue-router'

export const specialNeedRoutes: Array<RouteRecordRaw> = [
  {
    path: '/special-need',
    name: 'specialNeed',
    component: () => import('/@/layout/routerView/parent.vue'),
    meta: {
      title: '特殊需求配置',
      isLink: '',
      isHide: false,
      isKeepAlive: true,
      isAffix: false,
      isIframe: false,
      icon: '/src/assets/icons/configuration.svg'
    },
    children: [
      {
        path: '/special-need/config',
        name: 'specialNeedConfig',
        component: () => import('/@/views/specialNeed/config/index.vue'),
        meta: {
          title: '字段替换配置',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/special-need/config/detail/:id',
        name: 'specialNeedConfigDetail',
        component: () => import('/@/views/specialNeed/config/detail.vue'),
        meta: {
          title: '配置详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/special-need/config/edit/:id?',
        name: 'specialNeedConfigEdit',
        component: () => import('/@/views/specialNeed/config/edit.vue'),
        meta: {
          title: '编辑配置',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/special-need/limit',
        name: 'specialNeedLimit',
        component: () => import('/@/views/specialNeed/limit/index.vue'),
        meta: {
          title: '投递限制配置',
          isLink: '',
          isHide: false,
          isKeepAlive: true,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/special-need/limit/detail/:id',
        name: 'specialNeedLimitDetail',
        component: () => import('/@/views/specialNeed/limit/detail.vue'),
        meta: {
          title: '限制详情',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      },
      {
        path: '/special-need/limit/edit/:id?',
        name: 'specialNeedLimitEdit',
        component: () => import('/@/views/specialNeed/limit/edit.vue'),
        meta: {
          title: '编辑限制',
          isLink: '',
          isHide: true,
          isKeepAlive: false,
          isAffix: false,
          isIframe: false,
          icon: ''
        }
      }
    ]
  }
]
